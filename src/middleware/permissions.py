"""
Permission Decorators for QAK RBAC System

FastAPI decorators for role-based access control and permission checking.
Provides fine-grained access control for API endpoints.
"""

import functools
import hashlib
from typing import Optional, List, Union, Callable, Any
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from src.services.auth.jwt_service import JWTService
from src.services.auth.rbac_service import RB<PERSON>Service, Permission
from src.services.auth.token_blacklist import TokenBlacklistService
from src.database.models.user_organization import Role
from src.exceptions.auth_exceptions import (
    InvalidTokenException, TokenExpiredException, InsufficientPermissionsException,
    OrganizationAccessDeniedException, TokenBlacklistedException
)
import logging

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()

# Service instances
jwt_service = JWTService()
rbac_service = RBACService()
blacklist_service = TokenBlacklistService()


class AuthContext:
    """Authentication context for request processing."""
    
    def __init__(
        self,
        user_id: str,
        organization_id: str,
        role: str,
        email: Optional[str] = None,
        permissions: Optional[List[str]] = None
    ):
        self.user_id = user_id
        self.organization_id = organization_id
        self.role = role
        self.email = email
        self.permissions = permissions or []
    
    def has_role(self, required_role: Role) -> bool:
        """Check if user has required role."""
        return self.role == required_role.value
    
    def has_any_role(self, required_roles: List[Role]) -> bool:
        """Check if user has any of the required roles."""
        return any(self.has_role(role) for role in required_roles)
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission."""
        return permission in self.permissions


async def get_auth_context(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> AuthContext:
    """
    Extract and validate authentication context from JWT token.
    
    Args:
        credentials: HTTP Bearer credentials
        
    Returns:
        AuthContext with user information
        
    Raises:
        HTTPException: If token is invalid or user lacks access
    """
    try:
        token = credentials.credentials
        
        # Validate JWT token
        token_payload = jwt_service.validate_token(token)
        
        # Check if token is blacklisted
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        if await blacklist_service.is_blacklisted(
            token_jti=token_payload.jti,
            token_hash=token_hash
        ):
            raise TokenBlacklistedException()
        
        # Get user permissions
        permissions = await rbac_service.get_user_permissions_in_organization(
            user_id=token_payload.sub,
            organization_id=token_payload.organization_id
        )
        
        return AuthContext(
            user_id=token_payload.sub,
            organization_id=token_payload.organization_id,
            role=token_payload.role,
            email=token_payload.email,
            permissions=list(permissions)
        )
        
    except TokenBlacklistedException:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has been revoked",
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.warning(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"}
        )


def require_role(required_role: Union[Role, List[Role]]):
    """
    Decorator to require specific role(s) for endpoint access.
    
    Args:
        required_role: Required role or list of roles
        
    Returns:
        Decorator function
    """
    if isinstance(required_role, Role):
        required_roles = [required_role]
    else:
        required_roles = required_role
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get auth context from dependency injection
            auth_context = None
            for arg in args:
                if isinstance(arg, AuthContext):
                    auth_context = arg
                    break
            
            if not auth_context:
                # Try to get from kwargs
                auth_context = kwargs.get('auth_context')
            
            if not auth_context:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check role
            if not auth_context.has_any_role(required_roles):
                role_names = [role.value for role in required_roles]
                logger.warning(
                    f"Access denied: user {auth_context.user_id} lacks required role(s) {role_names}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Required role(s): {', '.join(role_names)}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_permission(required_permission: Union[str, Permission, List[Union[str, Permission]]]):
    """
    Decorator to require specific permission(s) for endpoint access.
    
    Args:
        required_permission: Required permission or list of permissions
        
    Returns:
        Decorator function
    """
    if isinstance(required_permission, (str, Permission)):
        required_permissions = [str(required_permission)]
    else:
        required_permissions = [str(perm) for perm in required_permission]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get auth context
            auth_context = None
            for arg in args:
                if isinstance(arg, AuthContext):
                    auth_context = arg
                    break
            
            if not auth_context:
                auth_context = kwargs.get('auth_context')
            
            if not auth_context:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check permissions
            for permission in required_permissions:
                has_permission = await rbac_service.check_permission(
                    user_id=auth_context.user_id,
                    organization_id=auth_context.organization_id,
                    required_permission=permission
                )
                
                if not has_permission:
                    logger.warning(
                        f"Access denied: user {auth_context.user_id} lacks permission {permission}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Required permission: {permission}"
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_organization_access(organization_id_param: str = "organization_id"):
    """
    Decorator to require access to specific organization.
    
    Args:
        organization_id_param: Parameter name containing organization ID
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get auth context
            auth_context = None
            for arg in args:
                if isinstance(arg, AuthContext):
                    auth_context = arg
                    break
            
            if not auth_context:
                auth_context = kwargs.get('auth_context')
            
            if not auth_context:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Get organization ID from parameters
            target_org_id = kwargs.get(organization_id_param)
            if not target_org_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing parameter: {organization_id_param}"
                )
            
            # Check organization access
            if auth_context.organization_id != target_org_id:
                # Check if user is ADMIN (can access any organization)
                if auth_context.role != Role.ADMIN.value:
                    logger.warning(
                        f"Access denied: user {auth_context.user_id} cannot access org {target_org_id}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Access denied to organization"
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def audit_action(action: str, resource_type: str = "unknown"):
    """
    Decorator to audit user actions for compliance and security monitoring.
    
    Args:
        action: Action being performed
        resource_type: Type of resource being accessed
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get auth context
            auth_context = None
            for arg in args:
                if isinstance(arg, AuthContext):
                    auth_context = arg
                    break
            
            if not auth_context:
                auth_context = kwargs.get('auth_context')
            
            # Get resource ID if available
            resource_id = kwargs.get('id') or kwargs.get('resource_id') or kwargs.get('project_id')
            
            # Log audit event
            logger.info(
                f"AUDIT: user={auth_context.user_id if auth_context else 'unknown'} "
                f"org={auth_context.organization_id if auth_context else 'unknown'} "
                f"action={action} resource_type={resource_type} resource_id={resource_id}"
            )
            
            try:
                result = await func(*args, **kwargs)
                
                # Log successful completion
                logger.info(
                    f"AUDIT_SUCCESS: user={auth_context.user_id if auth_context else 'unknown'} "
                    f"action={action} resource_type={resource_type}"
                )
                
                return result
                
            except Exception as e:
                # Log failure
                logger.warning(
                    f"AUDIT_FAILURE: user={auth_context.user_id if auth_context else 'unknown'} "
                    f"action={action} resource_type={resource_type} error={str(e)}"
                )
                raise
        
        return wrapper
    return decorator


# Convenience decorators for common permission combinations
def require_admin():
    """Decorator to require ADMIN role."""
    return require_role(Role.ADMIN)


def require_org_admin():
    """Decorator to require ORG_ADMIN or ADMIN role."""
    return require_role([Role.ORG_ADMIN, Role.ADMIN])


def require_user_or_above():
    """Decorator to require USER, ORG_ADMIN, or ADMIN role."""
    return require_role([Role.USER, Role.ORG_ADMIN, Role.ADMIN])


def require_project_access():
    """Decorator to require project access permissions."""
    return require_permission([Permission.READ_PROJECTS])


def require_user_management():
    """Decorator to require user management permissions."""
    return require_permission([Permission.MANAGE_USERS])



