"""
QAK Middleware Package

Custom middleware for authentication, rate limiting, and security.
"""

from .rate_limiting import RateLimitMiddleware, rate_limit_dependency
from .permissions import (
    AuthContext, get_auth_context, require_role, require_permission,
    require_organization_access, audit_action, require_admin,
    require_org_admin, require_user_or_above, require_project_access,
    require_user_management
)
from .auth_middleware import AuthMiddleware, get_auth_middleware, get_current_auth_context
from .organization_middleware import OrganizationMiddleware, get_organization_middleware, get_current_organization_context

__all__ = [
    "RateLimitMiddleware",
    "rate_limit_dependency",
    "AuthContext",
    "get_auth_context",
    "require_role",
    "require_permission",
    "require_organization_access",
    "audit_action",
    "require_admin",
    "require_org_admin",
    "require_user_or_above",
    "require_project_access",
    "require_user_management",
    "AuthMiddleware",
    "get_auth_middleware",
    "get_current_auth_context",
    "OrganizationMiddleware",
    "get_organization_middleware",
    "get_current_organization_context",
]
