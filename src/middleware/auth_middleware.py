"""
Authentication Middleware for QAK Multi-Tenant System

FastAPI middleware for JWT token extraction, validation, and user context injection.
Handles authentication for protected endpoints while allowing public access to auth routes.
"""

import hashlib
from typing import Set, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from src.services.auth.jwt_service import JWTService, TokenType
from src.services.auth.token_blacklist import TokenBlacklistService
from src.services.auth.rbac_service import RBACService
from src.database.models.user import User
from src.database.models.organization import Organization
from src.database.models.user_organization import UserOrganization
import logging

logger = logging.getLogger(__name__)


class AuthMiddleware:
    """Authentication middleware for JWT token processing."""
    
    def __init__(self):
        """Initialize authentication middleware."""
        self.jwt_service = JWTService()
        self.blacklist_service = TokenBlacklistService()
        self.rbac_service = RBACService()
        
        # Public endpoints that don't require authentication
        self.public_endpoints: Set[str] = {
            "/api/auth/register",
            "/api/auth/login", 
            "/api/auth/refresh",
            "/api/auth/password-strength",
            "/api/health",
            "/api/llm/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico",
        }
        
        # Endpoints that start with these prefixes are public
        self.public_prefixes: Set[str] = {
            "/static/",
            "/assets/",
            "/api/health",
        }
        
        logger.info("Authentication middleware initialized")
    
    def is_public_endpoint(self, path: str) -> bool:
        """
        Check if endpoint is public (doesn't require authentication).
        
        Args:
            path: Request path
            
        Returns:
            True if endpoint is public, False otherwise
        """
        # Check exact matches
        if path in self.public_endpoints:
            return True
        
        # Check prefix matches
        for prefix in self.public_prefixes:
            if path.startswith(prefix):
                return True
        
        return False
    
    def extract_token_from_header(self, request: Request) -> Optional[str]:
        """
        Extract JWT token from Authorization header.
        
        Args:
            request: FastAPI request object
            
        Returns:
            JWT token string or None if not found
        """
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return None
        
        # Check for Bearer token format
        if not auth_header.startswith("Bearer "):
            return None
        
        return auth_header[7:]  # Remove "Bearer " prefix
    
    async def validate_and_inject_context(self, request: Request, token: str) -> bool:
        """
        Validate JWT token and inject user context into request.
        
        Args:
            request: FastAPI request object
            token: JWT token string
            
        Returns:
            True if validation successful, False otherwise
        """
        try:
            # Validate JWT token
            token_payload = self.jwt_service.validate_token(token)
            
            # Ensure it's an access token
            if token_payload.token_type != TokenType.ACCESS:
                logger.warning("Invalid token type for API access")
                return False
            
            # Check if token is blacklisted
            token_hash = hashlib.sha256(token.encode()).hexdigest()
            if await self.blacklist_service.is_blacklisted(
                token_jti=token_payload.jti,
                token_hash=token_hash
            ):
                logger.warning("Blacklisted token used")
                return False
            
            # Verify user exists and is active
            user = await User.find_one({"user_id": token_payload.sub})
            if not user or not user.is_active:
                logger.warning(f"User not found or inactive: {token_payload.sub}")
                return False
            
            # Verify organization exists and is active
            organization = await Organization.find_one({
                "organization_id": token_payload.organization_id
            })
            if not organization or not organization.is_active:
                logger.warning(f"Organization not found or inactive: {token_payload.organization_id}")
                return False
            
            # Verify user-organization relationship
            user_org = await UserOrganization.find_one({
                "user_id": token_payload.sub,
                "organization_id": token_payload.organization_id,
                "is_active": True
            })
            if not user_org:
                logger.warning(f"User-organization relationship not found: {token_payload.sub}")
                return False
            
            # Get user permissions
            permissions = await self.rbac_service.get_user_permissions_in_organization(
                user_id=token_payload.sub,
                organization_id=token_payload.organization_id
            )
            
            # Inject authentication context into request state
            request.state.auth_context = {
                "user_id": token_payload.sub,
                "organization_id": token_payload.organization_id,
                "role": token_payload.role,
                "email": token_payload.email,
                "permissions": list(permissions),
                "user": user,
                "organization": organization,
                "user_organization": user_org,
                "token_payload": token_payload,
            }
            
            # Update last accessed time
            user_org.update_last_accessed()
            await user_org.save()
            
            logger.debug(f"Authentication successful for user {token_payload.sub}")
            return True
            
        except Exception as e:
            logger.warning(f"Token validation failed: {e}")
            return False
    
    def create_auth_error_response(self, detail: str) -> JSONResponse:
        """
        Create standardized authentication error response.
        
        Args:
            detail: Error detail message
            
        Returns:
            JSONResponse with authentication error
        """
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "success": False,
                "error": "authentication_required",
                "message": detail
            },
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    async def __call__(self, request: Request, call_next):
        """
        Process request with authentication middleware.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response from next handler or authentication error
        """
        path = request.url.path
        method = request.method
        
        # Skip authentication for public endpoints
        if self.is_public_endpoint(path):
            logger.debug(f"Public endpoint accessed: {method} {path}")
            return await call_next(request)
        
        # Extract token from Authorization header
        token = self.extract_token_from_header(request)
        
        if not token:
            logger.warning(f"Missing authentication token for protected endpoint: {method} {path}")
            return self.create_auth_error_response("Authentication token required")
        
        # Validate token and inject context
        is_valid = await self.validate_and_inject_context(request, token)
        
        if not is_valid:
            logger.warning(f"Invalid authentication token for: {method} {path}")
            return self.create_auth_error_response("Invalid or expired authentication token")
        
        # Add security headers to response
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add authentication info to response headers (for debugging)
        if hasattr(request.state, 'auth_context'):
            auth_context = request.state.auth_context
            response.headers["X-User-ID"] = auth_context["user_id"]
            response.headers["X-Organization-ID"] = auth_context["organization_id"]
            response.headers["X-User-Role"] = auth_context["role"]
        
        return response
    
    def add_public_endpoint(self, endpoint: str):
        """
        Add an endpoint to the public endpoints list.
        
        Args:
            endpoint: Endpoint path to add
        """
        self.public_endpoints.add(endpoint)
        logger.info(f"Added public endpoint: {endpoint}")
    
    def add_public_prefix(self, prefix: str):
        """
        Add a prefix to the public prefixes list.
        
        Args:
            prefix: Path prefix to add
        """
        self.public_prefixes.add(prefix)
        logger.info(f"Added public prefix: {prefix}")
    
    def remove_public_endpoint(self, endpoint: str):
        """
        Remove an endpoint from the public endpoints list.
        
        Args:
            endpoint: Endpoint path to remove
        """
        self.public_endpoints.discard(endpoint)
        logger.info(f"Removed public endpoint: {endpoint}")
    
    def get_auth_context(self, request: Request) -> Optional[dict]:
        """
        Get authentication context from request state.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Authentication context dictionary or None
        """
        return getattr(request.state, 'auth_context', None)
    
    def require_authentication(self, request: Request) -> dict:
        """
        Get authentication context and raise error if not authenticated.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Authentication context dictionary
            
        Raises:
            HTTPException: If not authenticated
        """
        auth_context = self.get_auth_context(request)
        if not auth_context:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"}
            )
        return auth_context


# Global middleware instance
_auth_middleware: Optional[AuthMiddleware] = None


def get_auth_middleware() -> AuthMiddleware:
    """Get global authentication middleware instance."""
    global _auth_middleware
    
    if _auth_middleware is None:
        _auth_middleware = AuthMiddleware()
    
    return _auth_middleware


# FastAPI dependency for getting auth context
async def get_current_auth_context(request: Request) -> dict:
    """
    FastAPI dependency to get current authentication context.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Authentication context dictionary
        
    Raises:
        HTTPException: If not authenticated
    """
    middleware = get_auth_middleware()
    return middleware.require_authentication(request)
