"""
Rate Limiting Middleware for QAK Authentication

Simple in-memory rate limiting for authentication endpoints.
Protects against brute force attacks and abuse.
"""

import os
import time
from typing import Dict, <PERSON><PERSON>, Optional
from collections import defaultdict, deque
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple in-memory rate limiter using sliding window."""
    
    def __init__(self, max_requests: int, window_seconds: int):
        """
        Initialize rate limiter.
        
        Args:
            max_requests: Maximum requests allowed in window
            window_seconds: Time window in seconds
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, deque] = defaultdict(deque)
    
    def is_allowed(self, identifier: str) -> Tuple[bool, Optional[int]]:
        """
        Check if request is allowed for identifier.
        
        Args:
            identifier: Unique identifier (IP address, user ID, etc.)
            
        Returns:
            Tuple of (is_allowed, retry_after_seconds)
        """
        now = time.time()
        window_start = now - self.window_seconds
        
        # Clean old requests
        request_times = self.requests[identifier]
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check if under limit
        if len(request_times) < self.max_requests:
            request_times.append(now)
            return True, None
        
        # Calculate retry after time
        oldest_request = request_times[0]
        retry_after = int(oldest_request + self.window_seconds - now) + 1
        
        return False, retry_after
    
    def reset(self, identifier: str):
        """Reset rate limit for identifier."""
        if identifier in self.requests:
            del self.requests[identifier]


class RateLimitMiddleware:
    """Rate limiting middleware for FastAPI."""
    
    def __init__(self):
        """Initialize rate limiting middleware."""
        # Configuration from environment
        self.auth_max_attempts = int(os.getenv("AUTH_MAX_LOGIN_ATTEMPTS", "5"))
        self.auth_window_minutes = int(os.getenv("AUTH_LOGIN_ATTEMPT_WINDOW_MINUTES", "5"))
        
        # Rate limiters for different endpoint types
        self.auth_limiter = RateLimiter(
            max_requests=self.auth_max_attempts,
            window_seconds=self.auth_window_minutes * 60
        )
        
        # General API rate limiter (more permissive)
        self.api_limiter = RateLimiter(
            max_requests=100,  # 100 requests per minute
            window_seconds=60
        )
        
        logger.info(f"Rate limiting initialized: {self.auth_max_attempts} auth attempts per {self.auth_window_minutes} minutes")
    
    def get_client_identifier(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Try to get real IP from headers (for reverse proxy setups)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    def is_auth_endpoint(self, path: str) -> bool:
        """Check if path is an authentication endpoint."""
        auth_endpoints = [
            "/api/auth/login",
            "/api/auth/register", 
            "/api/auth/refresh",
            "/api/auth/password-reset",
        ]
        return path in auth_endpoints
    
    async def __call__(self, request: Request, call_next):
        """Process request with rate limiting."""
        path = request.url.path
        client_id = self.get_client_identifier(request)
        
        # Apply rate limiting based on endpoint type
        if self.is_auth_endpoint(path):
            is_allowed, retry_after = self.auth_limiter.is_allowed(client_id)
            limiter_type = "auth"
        else:
            is_allowed, retry_after = self.api_limiter.is_allowed(client_id)
            limiter_type = "api"
        
        if not is_allowed:
            logger.warning(f"Rate limit exceeded for {client_id} on {path} ({limiter_type})")
            
            headers = {}
            if retry_after:
                headers["Retry-After"] = str(retry_after)
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "success": False,
                    "error": "rate_limit_exceeded",
                    "message": "Too many requests. Please try again later.",
                    "retry_after": retry_after
                },
                headers=headers
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        if self.is_auth_endpoint(path):
            remaining = max(0, self.auth_max_attempts - len(self.auth_limiter.requests[client_id]))
            response.headers["X-RateLimit-Limit"] = str(self.auth_max_attempts)
            response.headers["X-RateLimit-Remaining"] = str(remaining)
            response.headers["X-RateLimit-Window"] = str(self.auth_window_minutes * 60)
        
        return response


# Global rate limiter instance
_rate_limiter: Optional[RateLimitMiddleware] = None


def get_rate_limiter() -> RateLimitMiddleware:
    """Get global rate limiter instance."""
    global _rate_limiter
    
    if _rate_limiter is None:
        _rate_limiter = RateLimitMiddleware()
    
    return _rate_limiter


async def rate_limit_dependency(request: Request):
    """
    FastAPI dependency for rate limiting specific endpoints.
    Can be used as a dependency in route definitions.
    """
    limiter = get_rate_limiter()
    client_id = limiter.get_client_identifier(request)
    path = request.url.path
    
    if limiter.is_auth_endpoint(path):
        is_allowed, retry_after = limiter.auth_limiter.is_allowed(client_id)
        
        if not is_allowed:
            logger.warning(f"Rate limit exceeded for {client_id} on {path}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many requests. Please try again later.",
                headers={"Retry-After": str(retry_after)} if retry_after else {}
            )


def reset_rate_limit(identifier: str, endpoint_type: str = "auth"):
    """
    Reset rate limit for a specific identifier.
    Useful for successful authentications or admin overrides.
    """
    limiter = get_rate_limiter()
    
    if endpoint_type == "auth":
        limiter.auth_limiter.reset(identifier)
    elif endpoint_type == "api":
        limiter.api_limiter.reset(identifier)
    
    logger.info(f"Rate limit reset for {identifier} ({endpoint_type})")


def get_rate_limit_status(identifier: str, endpoint_type: str = "auth") -> Dict[str, int]:
    """
    Get current rate limit status for an identifier.
    
    Returns:
        Dictionary with current usage and limits
    """
    limiter = get_rate_limiter()
    
    if endpoint_type == "auth":
        rate_limiter = limiter.auth_limiter
        max_requests = limiter.auth_max_attempts
        window_seconds = limiter.auth_window_minutes * 60
    else:
        rate_limiter = limiter.api_limiter
        max_requests = 100
        window_seconds = 60
    
    # Clean old requests
    now = time.time()
    window_start = now - window_seconds
    request_times = rate_limiter.requests[identifier]
    
    while request_times and request_times[0] < window_start:
        request_times.popleft()
    
    current_requests = len(request_times)
    remaining = max(0, max_requests - current_requests)
    
    return {
        "limit": max_requests,
        "current": current_requests,
        "remaining": remaining,
        "window_seconds": window_seconds,
        "reset_time": int(request_times[0] + window_seconds) if request_times else int(now)
    }
