"""
Organization Middleware for QAK Multi-Tenant System

Middleware for organization context management, validation, and multi-organization switching.
Handles organization-scoped data access and context injection for repositories.
"""

from typing import Optional, Dict, Any
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from src.services.organization.organization_service import OrganizationService
from src.database.models.organization import Organization
from src.database.models.user_organization import UserOrganization, Role
from src.repositories.base_repository import MultiTenantRepository
import logging

logger = logging.getLogger(__name__)


class OrganizationMiddleware:
    """Middleware for organization context management."""
    
    def __init__(self):
        """Initialize organization middleware."""
        self.organization_service = OrganizationService()
        
        # Endpoints that require organization context
        self.org_scoped_prefixes = {
            "/api/projects",
            "/api/test-cases", 
            "/api/executions",
            "/api/artifacts",
            "/api/environments",
            "/api/codegen",
            "/api/organizations",  # Organization management endpoints
        }
        
        # Endpoints that don't need organization context
        self.org_free_endpoints = {
            "/api/auth/me",
            "/api/auth/logout",
            "/api/user/organizations",  # List user's organizations
            "/api/user/switch-organization",  # Switch organization context
        }
        
        logger.info("Organization middleware initialized")
    
    def requires_organization_context(self, path: str) -> bool:
        """
        Check if endpoint requires organization context.
        
        Args:
            path: Request path
            
        Returns:
            True if organization context is required
        """
        # Check if endpoint is explicitly org-free
        if path in self.org_free_endpoints:
            return False
        
        # Check if path starts with org-scoped prefix
        for prefix in self.org_scoped_prefixes:
            if path.startswith(prefix):
                return True
        
        return False
    
    def extract_organization_context(self, request: Request) -> Optional[str]:
        """
        Extract organization context from request.
        
        Priority order:
        1. X-Organization-ID header
        2. organization_id query parameter
        3. organization_id from JWT token (auth context)
        4. Default organization from user's primary membership
        
        Args:
            request: FastAPI request object
            
        Returns:
            Organization ID or None if not found
        """
        # 1. Check X-Organization-ID header
        org_header = request.headers.get("X-Organization-ID")
        if org_header:
            logger.debug(f"Organization context from header: {org_header}")
            return org_header
        
        # 2. Check organization_id query parameter
        org_query = request.query_params.get("organization_id")
        if org_query:
            logger.debug(f"Organization context from query: {org_query}")
            return org_query
        
        # 3. Check auth context (from JWT token)
        auth_context = getattr(request.state, 'auth_context', None)
        if auth_context and auth_context.get("organization_id"):
            org_id = auth_context["organization_id"]
            logger.debug(f"Organization context from auth: {org_id}")
            return org_id
        
        return None
    
    async def validate_organization_access(
        self,
        user_id: str,
        organization_id: str
    ) -> bool:
        """
        Validate that user has access to organization.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            
        Returns:
            True if user has access, False otherwise
        """
        try:
            return await self.organization_service.can_user_access_organization(
                user_id=user_id,
                organization_id=organization_id
            )
        except Exception as e:
            logger.error(f"Error validating organization access: {e}")
            return False
    
    async def get_user_primary_organization(self, user_id: str) -> Optional[str]:
        """
        Get user's primary organization (first active organization).
        
        Args:
            user_id: User identifier
            
        Returns:
            Primary organization ID or None
        """
        try:
            organizations = await self.organization_service.get_organizations_for_user(user_id)
            if organizations:
                # Return the first active organization
                return organizations[0]["organization_id"]
            return None
        except Exception as e:
            logger.error(f"Error getting primary organization: {e}")
            return None
    
    async def inject_organization_context(
        self,
        request: Request,
        organization_id: str
    ) -> bool:
        """
        Inject organization context into request state.
        
        Args:
            request: FastAPI request object
            organization_id: Organization identifier
            
        Returns:
            True if injection successful, False otherwise
        """
        try:
            # Get organization details
            organization = await self.organization_service.get_organization_by_id(organization_id)
            if not organization or not organization.is_active:
                logger.warning(f"Organization not found or inactive: {organization_id}")
                return False
            
            # Get auth context
            auth_context = getattr(request.state, 'auth_context', None)
            if not auth_context:
                logger.warning("No auth context found for organization injection")
                return False
            
            user_id = auth_context["user_id"]
            
            # Validate user access to organization
            has_access = await self.validate_organization_access(user_id, organization_id)
            if not has_access:
                logger.warning(f"User {user_id} denied access to organization {organization_id}")
                return False
            
            # Get user's role in this organization
            user_role = await self.organization_service.get_user_role_in_organization(
                user_id=user_id,
                organization_id=organization_id
            )
            
            if not user_role:
                logger.warning(f"User {user_id} has no role in organization {organization_id}")
                return False
            
            # Inject organization context
            request.state.organization_context = {
                "organization_id": organization_id,
                "organization": organization,
                "user_role": user_role.value,
                "user_id": user_id,
            }
            
            # Update auth context with current organization info
            auth_context["current_organization_id"] = organization_id
            auth_context["current_organization"] = organization
            auth_context["current_role"] = user_role.value
            
            logger.debug(f"Organization context injected: {organization_id} for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error injecting organization context: {e}")
            return False
    
    def create_organization_error_response(self, detail: str, status_code: int = 403) -> JSONResponse:
        """
        Create standardized organization error response.
        
        Args:
            detail: Error detail message
            status_code: HTTP status code
            
        Returns:
            JSONResponse with organization error
        """
        return JSONResponse(
            status_code=status_code,
            content={
                "success": False,
                "error": "organization_access_denied",
                "message": detail
            }
        )
    
    async def setup_repository_contexts(self, request: Request):
        """
        Set up organization context for all multi-tenant repositories.
        
        Args:
            request: FastAPI request object
        """
        org_context = getattr(request.state, 'organization_context', None)
        if not org_context:
            return
        
        organization_id = org_context["organization_id"]
        
        # This would typically be done through dependency injection
        # For now, we'll store the organization ID in request state
        # so repositories can access it
        request.state.current_organization_id = organization_id
        
        logger.debug(f"Repository contexts set for organization: {organization_id}")
    
    async def __call__(self, request: Request, call_next):
        """
        Process request with organization middleware.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain
            
        Returns:
            Response from next handler or organization error
        """
        path = request.url.path
        method = request.method
        
        # Skip organization processing for endpoints that don't need it
        if not self.requires_organization_context(path):
            logger.debug(f"Organization-free endpoint: {method} {path}")
            return await call_next(request)
        
        # Get auth context (should be set by auth middleware)
        auth_context = getattr(request.state, 'auth_context', None)
        if not auth_context:
            logger.warning(f"No auth context for organization-scoped endpoint: {method} {path}")
            return self.create_organization_error_response(
                "Authentication required for organization access",
                status_code=401
            )
        
        user_id = auth_context["user_id"]
        
        # Extract organization context
        organization_id = self.extract_organization_context(request)
        
        # If no organization context provided, try to get user's primary organization
        if not organization_id:
            organization_id = await self.get_user_primary_organization(user_id)
            
            if not organization_id:
                logger.warning(f"No organization context for user {user_id}")
                return self.create_organization_error_response(
                    "Organization context required. Please specify organization or join an organization."
                )
        
        # Inject organization context
        success = await self.inject_organization_context(request, organization_id)
        if not success:
            return self.create_organization_error_response(
                f"Access denied to organization: {organization_id}"
            )
        
        # Set up repository contexts
        await self.setup_repository_contexts(request)
        
        # Process request
        response = await call_next(request)
        
        # Add organization info to response headers
        response.headers["X-Current-Organization"] = organization_id
        
        return response
    
    def get_organization_context(self, request: Request) -> Optional[Dict[str, Any]]:
        """
        Get organization context from request state.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Organization context dictionary or None
        """
        return getattr(request.state, 'organization_context', None)
    
    def require_organization_context(self, request: Request) -> Dict[str, Any]:
        """
        Get organization context and raise error if not available.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Organization context dictionary
            
        Raises:
            HTTPException: If no organization context
        """
        org_context = self.get_organization_context(request)
        if not org_context:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Organization context required"
            )
        return org_context
    
    async def switch_organization_context(
        self,
        request: Request,
        new_organization_id: str
    ) -> bool:
        """
        Switch to a different organization context.
        
        Args:
            request: FastAPI request object
            new_organization_id: New organization identifier
            
        Returns:
            True if switch successful, False otherwise
        """
        auth_context = getattr(request.state, 'auth_context', None)
        if not auth_context:
            return False
        
        user_id = auth_context["user_id"]
        
        # Validate access to new organization
        has_access = await self.validate_organization_access(user_id, new_organization_id)
        if not has_access:
            return False
        
        # Inject new organization context
        return await self.inject_organization_context(request, new_organization_id)


# Global middleware instance
_organization_middleware: Optional[OrganizationMiddleware] = None


def get_organization_middleware() -> OrganizationMiddleware:
    """Get global organization middleware instance."""
    global _organization_middleware
    
    if _organization_middleware is None:
        _organization_middleware = OrganizationMiddleware()
    
    return _organization_middleware


# FastAPI dependency for getting organization context
async def get_current_organization_context(request: Request) -> Dict[str, Any]:
    """
    FastAPI dependency to get current organization context.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Organization context dictionary
        
    Raises:
        HTTPException: If no organization context
    """
    middleware = get_organization_middleware()
    return middleware.require_organization_context(request)
