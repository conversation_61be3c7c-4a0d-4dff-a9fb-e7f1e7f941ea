"""
Multi-Tenant CodegenSession Repository for QAK

Organization-aware repository for CodegenSession entities with automatic organization_id scoping.
Provides specialized queries for code generation analytics and session management.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.repositories.base_repository import MultiTenantRepository
from src.database.models.codegen_session import CodegenSession, CodegenStatus, TargetLanguage
from src.exceptions.auth_exceptions import OrganizationAccessDeniedException
import logging

logger = logging.getLogger(__name__)


class CodegenSessionRepository(MultiTenantRepository[CodegenSession]):
    """Multi-tenant repository for CodegenSession entities."""
    
    def __init__(self):
        """Initialize codegen session repository."""
        super().__init__(CodegenSession, "organization_id")
        logger.debug("Multi-tenant CodegenSessionRepository initialized")
    
    async def find_by_session_id(self, session_id: str) -> Optional[CodegenSession]:
        """
        Find codegen session by session_id within organization context.
        
        Args:
            session_id: Session identifier
            
        Returns:
            CodegenSession instance or None if not found in organization
        """
        sessions = await self.find_all({"session_id": session_id})
        return sessions[0] if sessions else None
    
    async def find_by_status(
        self,
        status: str,
        limit: Optional[int] = None
    ) -> List[CodegenSession]:
        """
        Find sessions by status within organization context.
        
        Args:
            status: Session status
            limit: Maximum number of sessions to return
            
        Returns:
            List of sessions with the specified status
        """
        return await self.find_all(
            filter_dict={"status": status},
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def find_by_target_language(
        self,
        target_language: str,
        limit: Optional[int] = None
    ) -> List[CodegenSession]:
        """
        Find sessions by target language within organization context.
        
        Args:
            target_language: Target programming language
            limit: Maximum number of sessions to return
            
        Returns:
            List of sessions for the target language
        """
        return await self.find_all(
            filter_dict={"target_language": target_language},
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def find_recent_sessions(
        self,
        limit: int = 50,
        hours: int = 24
    ) -> List[CodegenSession]:
        """
        Find recent sessions within organization context.
        
        Args:
            limit: Maximum number of sessions to return
            hours: Number of hours to look back
            
        Returns:
            List of recent sessions
        """
        since = datetime.utcnow() - timedelta(hours=hours)
        
        return await self.find_all(
            filter_dict={"created_at": {"$gte": since}},
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def find_successful_sessions(
        self,
        limit: Optional[int] = None,
        days: int = 30
    ) -> List[CodegenSession]:
        """
        Find successful sessions within organization context.
        
        Args:
            limit: Maximum number of sessions to return
            days: Number of days to look back
            
        Returns:
            List of successful sessions
        """
        since = datetime.utcnow() - timedelta(days=days)
        
        return await self.find_all(
            filter_dict={
                "status": CodegenStatus.COMPLETED.value,
                "created_at": {"$gte": since}
            },
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def find_failed_sessions(
        self,
        limit: int = 50,
        days: int = 7
    ) -> List[CodegenSession]:
        """
        Find failed sessions within organization context.
        
        Args:
            limit: Maximum number of sessions to return
            days: Number of days to look back
            
        Returns:
            List of failed sessions
        """
        since = datetime.utcnow() - timedelta(days=days)
        
        return await self.find_all(
            filter_dict={
                "status": {"$in": [CodegenStatus.FAILED.value, CodegenStatus.ERROR.value]},
                "created_at": {"$gte": since}
            },
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def find_sessions_with_generated_code(
        self,
        limit: int = 50
    ) -> List[CodegenSession]:
        """
        Find sessions that have generated code within organization.
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of sessions with generated code
        """
        return await self.find_all(
            filter_dict={
                "generated_code": {"$exists": True, "$ne": "", "$ne": None}
            },
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def get_codegen_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get code generation statistics for the current organization.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with codegen statistics
        """
        base_stats = await self.get_organization_statistics()
        
        try:
            since = datetime.utcnow() - timedelta(days=days)
            
            # Get sessions from the specified period
            recent_sessions = await self.find_all({
                "created_at": {"$gte": since}
            })
            
            # Get all sessions for total statistics
            all_sessions = await self.find_all()
            
            # Calculate statistics
            total_sessions = len(all_sessions)
            recent_sessions_count = len(recent_sessions)
            
            # Status breakdown
            status_counts = {}
            language_counts = {}
            total_execution_time = 0
            successful_sessions = 0
            failed_sessions = 0
            sessions_with_code = 0
            
            for session in recent_sessions:
                # Count by status
                status = session.status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Count by language
                language = session.target_language
                language_counts[language] = language_counts.get(language, 0) + 1
                
                # Execution time statistics
                if session.execution_time_ms:
                    total_execution_time += session.execution_time_ms
                
                # Success/failure counts
                if status == CodegenStatus.COMPLETED.value:
                    successful_sessions += 1
                elif status in [CodegenStatus.FAILED.value, CodegenStatus.ERROR.value]:
                    failed_sessions += 1
                
                # Sessions with generated code
                if session.generated_code:
                    sessions_with_code += 1
            
            # Calculate success rate
            success_rate = (successful_sessions / recent_sessions_count * 100) if recent_sessions_count > 0 else 0
            
            # Calculate average execution time
            avg_execution_time = total_execution_time / recent_sessions_count if recent_sessions_count > 0 else 0
            
            base_stats.update({
                "period_days": days,
                "total_sessions": total_sessions,
                "recent_sessions": recent_sessions_count,
                "successful_sessions": successful_sessions,
                "failed_sessions": failed_sessions,
                "sessions_with_code": sessions_with_code,
                "success_rate_percent": round(success_rate, 2),
                "code_generation_rate_percent": round((sessions_with_code / recent_sessions_count * 100), 2) if recent_sessions_count > 0 else 0,
                "status_breakdown": status_counts,
                "language_breakdown": language_counts,
                "avg_execution_time_ms": round(avg_execution_time, 2),
                "avg_execution_time_seconds": round(avg_execution_time / 1000, 2) if avg_execution_time > 0 else 0,
            })
            
            return base_stats
            
        except Exception as e:
            logger.error(f"Failed to get codegen statistics: {e}")
            base_stats["error"] = str(e)
            return base_stats
    
    async def update_session_status(
        self,
        session_id: str,
        new_status: str,
        error_message: Optional[str] = None
    ) -> Optional[CodegenSession]:
        """
        Update session status within organization context.
        
        Args:
            session_id: Session identifier
            new_status: New status value
            error_message: Optional error message for failed sessions
            
        Returns:
            Updated session or None if not found
        """
        session = await self.find_by_session_id(session_id)
        if not session:
            return None
        
        session.status = new_status
        
        if error_message:
            session.error_message = error_message
        
        if new_status in [CodegenStatus.COMPLETED.value, CodegenStatus.FAILED.value, CodegenStatus.ERROR.value]:
            session.completed_at = datetime.utcnow()
            
            # Calculate execution time if not already set
            if not session.execution_time_ms and session.started_at:
                execution_time = (session.completed_at - session.started_at).total_seconds() * 1000
                session.execution_time_ms = int(execution_time)
        
        return await self.update(session)
    
    async def update_generated_code(
        self,
        session_id: str,
        generated_code: str
    ) -> Optional[CodegenSession]:
        """
        Update generated code for a session within organization context.
        
        Args:
            session_id: Session identifier
            generated_code: Generated code content
            
        Returns:
            Updated session or None if not found
        """
        session = await self.find_by_session_id(session_id)
        if not session:
            return None
        
        session.generated_code = generated_code
        
        # Update status to completed if not already set
        if session.status == CodegenStatus.IN_PROGRESS.value:
            session.status = CodegenStatus.COMPLETED.value
            session.completed_at = datetime.utcnow()
            
            if not session.execution_time_ms and session.started_at:
                execution_time = (session.completed_at - session.started_at).total_seconds() * 1000
                session.execution_time_ms = int(execution_time)
        
        return await self.update(session)
    
    async def search_sessions(
        self,
        search_term: str,
        limit: int = 50
    ) -> List[CodegenSession]:
        """
        Search sessions by various fields within organization.
        
        Args:
            search_term: Term to search for
            limit: Maximum number of results
            
        Returns:
            List of matching sessions
        """
        return await self.find_all(
            filter_dict={
                "$or": [
                    {"session_id": {"$regex": search_term, "$options": "i"}},
                    {"prompt": {"$regex": search_term, "$options": "i"}},
                    {"target_language": {"$regex": search_term, "$options": "i"}},
                    {"generated_code": {"$regex": search_term, "$options": "i"}},
                ]
            },
            limit=limit,
            sort=[("created_at", -1)]
        )
    
    async def delete_old_sessions(
        self,
        days: int = 90,
        keep_successful: bool = True
    ) -> int:
        """
        Delete old sessions within organization context.
        
        Args:
            days: Delete sessions older than this many days
            keep_successful: Whether to keep successful sessions with generated code
            
        Returns:
            Number of sessions deleted
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Build deletion criteria
        delete_criteria = {"created_at": {"$lt": cutoff_date}}
        
        if keep_successful:
            delete_criteria["$or"] = [
                {"status": {"$ne": CodegenStatus.COMPLETED.value}},
                {"generated_code": {"$in": [None, ""]}},
            ]
        
        # Find sessions to delete
        sessions_to_delete = await self.find_all(delete_criteria)
        
        # Delete sessions
        deleted_count = 0
        for session in sessions_to_delete:
            if await self.delete(session):
                deleted_count += 1
        
        logger.info(f"Deleted {deleted_count} old codegen sessions from organization")
        return deleted_count
