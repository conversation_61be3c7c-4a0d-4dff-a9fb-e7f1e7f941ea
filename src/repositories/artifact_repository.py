"""
Multi-Tenant Artifact Repository for QAK

Organization-aware repository for Artifact entities with automatic organization_id scoping.
Provides specialized queries for artifact management, storage, and cleanup.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from src.repositories.base_repository import MultiTenantRepository
from src.database.models.artifact import Artifact, ArtifactType, ArtifactStatus
from src.exceptions.auth_exceptions import OrganizationAccessDeniedException
import logging

logger = logging.getLogger(__name__)


class ArtifactRepository(MultiTenantRepository[Artifact]):
    """Multi-tenant repository for Artifact entities."""
    
    def __init__(self):
        """Initialize artifact repository."""
        super().__init__(Artifact, "organization_id")
        logger.debug("Multi-tenant ArtifactRepository initialized")
    
    async def find_by_artifact_id(self, artifact_id: str) -> Optional[Artifact]:
        """
        Find artifact by artifact_id within organization context.
        
        Args:
            artifact_id: Artifact identifier
            
        Returns:
            Artifact instance or None if not found in organization
        """
        artifacts = await self.find_all({"artifact_id": artifact_id})
        return artifacts[0] if artifacts else None
    
    async def find_by_execution_id(
        self,
        execution_id: str,
        artifact_type: Optional[str] = None
    ) -> List[Artifact]:
        """
        Find artifacts by execution_id within organization context.
        
        Args:
            execution_id: Execution identifier
            artifact_type: Optional artifact type filter
            
        Returns:
            List of artifacts for the execution
        """
        filter_dict = {"execution_id": execution_id}
        if artifact_type:
            filter_dict["type"] = artifact_type
        
        return await self.find_all(
            filter_dict=filter_dict,
            sort=[("collected_at", -1)]
        )
    
    async def find_by_project_id(
        self,
        project_id: str,
        artifact_type: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[Artifact]:
        """
        Find artifacts by project_id within organization context.
        
        Args:
            project_id: Project identifier
            artifact_type: Optional artifact type filter
            limit: Maximum number of artifacts to return
            
        Returns:
            List of artifacts for the project
        """
        filter_dict = {"project_id": project_id}
        if artifact_type:
            filter_dict["type"] = artifact_type
        
        return await self.find_all(
            filter_dict=filter_dict,
            limit=limit,
            sort=[("collected_at", -1)]
        )
    
    async def find_by_type(
        self,
        artifact_type: str,
        limit: Optional[int] = None
    ) -> List[Artifact]:
        """
        Find artifacts by type within organization context.
        
        Args:
            artifact_type: Artifact type
            limit: Maximum number of artifacts to return
            
        Returns:
            List of artifacts of the specified type
        """
        return await self.find_all(
            filter_dict={"type": artifact_type},
            limit=limit,
            sort=[("collected_at", -1)]
        )
    
    async def find_by_status(
        self,
        status: str,
        limit: Optional[int] = None
    ) -> List[Artifact]:
        """
        Find artifacts by status within organization context.
        
        Args:
            status: Artifact status
            limit: Maximum number of artifacts to return
            
        Returns:
            List of artifacts with the specified status
        """
        return await self.find_all(
            filter_dict={"status": status},
            limit=limit,
            sort=[("collected_at", -1)]
        )
    
    async def find_expired_artifacts(self) -> List[Artifact]:
        """
        Find expired artifacts within organization context.
        
        Returns:
            List of expired artifacts that can be cleaned up
        """
        now = datetime.utcnow()
        
        return await self.find_all({
            "expires_at": {"$lt": now},
            "is_permanent": False
        })
    
    async def find_large_artifacts(
        self,
        min_size_mb: float = 10.0,
        limit: Optional[int] = None
    ) -> List[Artifact]:
        """
        Find large artifacts within organization context.
        
        Args:
            min_size_mb: Minimum size in megabytes
            limit: Maximum number of artifacts to return
            
        Returns:
            List of large artifacts
        """
        min_size_bytes = int(min_size_mb * 1024 * 1024)
        
        return await self.find_all(
            filter_dict={"size_bytes": {"$gte": min_size_bytes}},
            limit=limit,
            sort=[("size_bytes", -1)]
        )
    
    async def find_duplicates_by_hash(
        self,
        content_hash: str
    ) -> List[Artifact]:
        """
        Find duplicate artifacts by content hash within organization.
        
        Args:
            content_hash: Content hash to search for
            
        Returns:
            List of artifacts with the same content hash
        """
        return await self.find_all({
            "content_hash": content_hash,
            "content_hash": {"$ne": None}
        })
    
    async def get_artifact_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get artifact statistics for the current organization.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with artifact statistics
        """
        base_stats = await self.get_organization_statistics()
        
        try:
            since = datetime.utcnow() - timedelta(days=days)
            
            # Get artifacts from the specified period
            recent_artifacts = await self.find_all({
                "collected_at": {"$gte": since}
            })
            
            # Get all artifacts for total statistics
            all_artifacts = await self.find_all()
            
            # Calculate statistics
            total_artifacts = len(all_artifacts)
            recent_artifacts_count = len(recent_artifacts)
            
            # Type breakdown
            type_counts = {}
            status_counts = {}
            total_size_bytes = 0
            
            for artifact in all_artifacts:
                # Count by type
                artifact_type = artifact.type
                type_counts[artifact_type] = type_counts.get(artifact_type, 0) + 1
                
                # Count by status
                status = artifact.status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Size statistics
                if artifact.size_bytes:
                    total_size_bytes += artifact.size_bytes
            
            # Calculate storage usage
            total_size_mb = total_size_bytes / (1024 * 1024)
            total_size_gb = total_size_mb / 1024
            
            # Find expired artifacts
            expired_artifacts = await self.find_expired_artifacts()
            
            base_stats.update({
                "period_days": days,
                "total_artifacts": total_artifacts,
                "recent_artifacts": recent_artifacts_count,
                "type_breakdown": type_counts,
                "status_breakdown": status_counts,
                "total_size_bytes": total_size_bytes,
                "total_size_mb": round(total_size_mb, 2),
                "total_size_gb": round(total_size_gb, 2),
                "expired_artifacts": len(expired_artifacts),
                "avg_size_bytes": round(total_size_bytes / total_artifacts, 2) if total_artifacts > 0 else 0,
            })
            
            return base_stats
            
        except Exception as e:
            logger.error(f"Failed to get artifact statistics: {e}")
            base_stats["error"] = str(e)
            return base_stats
    
    async def cleanup_expired_artifacts(self) -> int:
        """
        Clean up expired artifacts within organization context.
        
        Returns:
            Number of artifacts cleaned up
        """
        expired_artifacts = await self.find_expired_artifacts()
        
        cleaned_count = 0
        for artifact in expired_artifacts:
            try:
                # Delete the artifact record
                if await self.delete(artifact):
                    cleaned_count += 1
                    logger.debug(f"Cleaned up expired artifact: {artifact.artifact_id}")
            except Exception as e:
                logger.error(f"Failed to cleanup artifact {artifact.artifact_id}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} expired artifacts from organization")
        
        return cleaned_count
    
    async def update_artifact_status(
        self,
        artifact_id: str,
        new_status: str,
        processed_at: Optional[datetime] = None
    ) -> Optional[Artifact]:
        """
        Update artifact status within organization context.
        
        Args:
            artifact_id: Artifact identifier
            new_status: New status value
            processed_at: Optional processing timestamp
            
        Returns:
            Updated artifact or None if not found
        """
        artifact = await self.find_by_artifact_id(artifact_id)
        if not artifact:
            return None
        
        artifact.status = new_status
        if processed_at:
            artifact.processed_at = processed_at
        elif new_status in [ArtifactStatus.PROCESSED.value, ArtifactStatus.AVAILABLE.value]:
            artifact.processed_at = datetime.utcnow()
        
        return await self.update(artifact)
    
    async def search_artifacts(
        self,
        search_term: str,
        limit: int = 50
    ) -> List[Artifact]:
        """
        Search artifacts by various fields within organization.
        
        Args:
            search_term: Term to search for
            limit: Maximum number of results
            
        Returns:
            List of matching artifacts
        """
        return await self.find_all(
            filter_dict={
                "$or": [
                    {"artifact_id": {"$regex": search_term, "$options": "i"}},
                    {"execution_id": {"$regex": search_term, "$options": "i"}},
                    {"project_id": {"$regex": search_term, "$options": "i"}},
                    {"original_name": {"$regex": search_term, "$options": "i"}},
                    {"step_name": {"$regex": search_term, "$options": "i"}},
                ]
            },
            limit=limit,
            sort=[("collected_at", -1)]
        )
    
    async def get_storage_usage_by_type(self) -> Dict[str, Dict[str, Any]]:
        """
        Get storage usage breakdown by artifact type within organization.
        
        Returns:
            Dictionary with storage usage by type
        """
        try:
            all_artifacts = await self.find_all()
            
            usage_by_type = {}
            
            for artifact in all_artifacts:
                artifact_type = artifact.type
                
                if artifact_type not in usage_by_type:
                    usage_by_type[artifact_type] = {
                        "count": 0,
                        "total_size_bytes": 0,
                        "total_size_mb": 0,
                        "avg_size_bytes": 0,
                    }
                
                usage_by_type[artifact_type]["count"] += 1
                
                if artifact.size_bytes:
                    usage_by_type[artifact_type]["total_size_bytes"] += artifact.size_bytes
            
            # Calculate derived values
            for type_data in usage_by_type.values():
                type_data["total_size_mb"] = round(type_data["total_size_bytes"] / (1024 * 1024), 2)
                if type_data["count"] > 0:
                    type_data["avg_size_bytes"] = round(type_data["total_size_bytes"] / type_data["count"], 2)
            
            return usage_by_type
            
        except Exception as e:
            logger.error(f"Failed to get storage usage by type: {e}")
            return {}
