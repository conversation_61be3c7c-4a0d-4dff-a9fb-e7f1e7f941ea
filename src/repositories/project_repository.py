"""
Multi-Tenant Project Repository for QAK

Organization-aware repository for Project entities with automatic organization_id scoping.
Extends the base MultiTenantRepository for organization-specific operations.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from src.repositories.base_repository import MultiTenantRepository
from src.database.models.project import Project
from src.exceptions.auth_exceptions import OrganizationAccessDeniedException
import logging

logger = logging.getLogger(__name__)


class ProjectRepository(MultiTenantRepository[Project]):
    """Multi-tenant repository for Project entities."""
    
    def __init__(self):
        """Initialize project repository."""
        super().__init__(Project, "organization_id")
        logger.debug("Multi-tenant ProjectRepository initialized")
    
    async def find_by_project_id(self, project_id: str) -> Optional[Project]:
        """
        Find project by project_id within organization context.
        
        Args:
            project_id: Project identifier
            
        Returns:
            Project instance or None if not found in organization
        """
        projects = await self.find_all({"project_id": project_id})
        return projects[0] if projects else None
    
    async def find_by_name(self, name: str) -> Optional[Project]:
        """
        Find project by name within organization context.
        
        Args:
            name: Project name
            
        Returns:
            Project instance or None if not found in organization
        """
        projects = await self.find_all({"name": name})
        return projects[0] if projects else None
    
    async def exists_by_project_id(self, project_id: str) -> bool:
        """
        Check if project exists by project_id within organization.
        
        Args:
            project_id: Project identifier
            
        Returns:
            True if project exists in organization, False otherwise
        """
        return await self.exists({"project_id": project_id})
    
    async def exists_by_name(self, name: str) -> bool:
        """
        Check if project exists by name within organization.
        
        Args:
            name: Project name
            
        Returns:
            True if project exists in organization, False otherwise
        """
        return await self.exists({"name": name})
    
    async def create_project(self, project: Project) -> Project:
        """
        Create a new project with duplicate checking within organization.
        
        Args:
            project: Project instance to create
            
        Returns:
            Created project with organization_id
            
        Raises:
            ValueError: If project_id or name already exists in organization
        """
        # Check for duplicate project_id within organization
        if await self.exists_by_project_id(project.project_id):
            raise ValueError(f"Project with ID '{project.project_id}' already exists in organization")
        
        # Check for duplicate name within organization
        if await self.exists_by_name(project.name):
            raise ValueError(f"Project with name '{project.name}' already exists in organization")
        
        return await self.create(project)
    
    async def update_project(self, project: Project) -> Project:
        """
        Update an existing project within organization context.
        
        Args:
            project: Project instance to update
            
        Returns:
            Updated project
            
        Raises:
            ValueError: If project doesn't exist in organization
        """
        # Verify project exists in organization
        existing = await self.find_by_project_id(project.project_id)
        if not existing:
            raise ValueError(f"Project '{project.project_id}' not found in organization")
        
        # Update timestamps
        project.updated_at = datetime.utcnow()
        
        return await self.update(project)
    
    async def get_projects_by_tags(self, tags: List[str]) -> List[Project]:
        """
        Get projects that contain any of the specified tags within organization.
        
        Args:
            tags: List of tags to search for
            
        Returns:
            List of projects containing any of the tags
        """
        return await self.find_all({
            "tags": {"$in": tags}
        })
    
    async def get_recent_projects(self, limit: int = 10) -> List[Project]:
        """
        Get recently updated projects within organization.
        
        Args:
            limit: Maximum number of projects to return
            
        Returns:
            List of recently updated projects
        """
        return await self.find_all(
            filter_dict={},
            limit=limit,
            sort=[("updated_at", -1)]
        )
    
    async def get_projects_with_test_suites(self) -> List[Project]:
        """
        Get projects that have test suites within organization.
        
        Returns:
            List of projects with test suites
        """
        return await self.find_all({
            "test_suites": {"$exists": True, "$ne": {}}
        })
    
    async def search_projects(
        self,
        search_term: str,
        limit: int = 50
    ) -> List[Project]:
        """
        Search projects by name or description within organization.
        
        Args:
            search_term: Term to search for
            limit: Maximum number of results
            
        Returns:
            List of matching projects
        """
        return await self.find_all(
            filter_dict={
                "$or": [
                    {"name": {"$regex": search_term, "$options": "i"}},
                    {"description": {"$regex": search_term, "$options": "i"}}
                ]
            },
            limit=limit,
            sort=[("updated_at", -1)]
        )
    
    async def get_project_statistics(self) -> Dict[str, Any]:
        """
        Get project statistics for the current organization.
        
        Returns:
            Dictionary with project statistics
        """
        base_stats = await self.get_organization_statistics()
        
        try:
            # Get additional project-specific statistics
            projects = await self.find_all()
            
            total_test_suites = 0
            total_test_cases = 0
            projects_with_environments = 0
            
            for project in projects:
                total_test_suites += len(project.test_suites)
                
                for suite in project.test_suites.values():
                    total_test_cases += len(suite.test_cases)
                
                if project.environments:
                    projects_with_environments += 1
            
            base_stats.update({
                "total_test_suites": total_test_suites,
                "total_test_cases": total_test_cases,
                "projects_with_environments": projects_with_environments,
                "avg_test_suites_per_project": total_test_suites / len(projects) if projects else 0,
                "avg_test_cases_per_project": total_test_cases / len(projects) if projects else 0,
            })
            
            return base_stats
            
        except Exception as e:
            logger.error(f"Failed to get project statistics: {e}")
            base_stats["error"] = str(e)
            return base_stats
    
    async def delete_project(self, project_id: str) -> bool:
        """
        Delete a project by project_id within organization context.
        
        Args:
            project_id: Project identifier
            
        Returns:
            True if project was deleted, False if not found
        """
        project = await self.find_by_project_id(project_id)
        if project:
            return await self.delete(project)
        return False
    
    async def get_projects_by_environment(self, environment_id: str) -> List[Project]:
        """
        Get projects that use a specific environment within organization.
        
        Args:
            environment_id: Environment identifier
            
        Returns:
            List of projects using the environment
        """
        return await self.find_all({
            "$or": [
                {"default_environment_id": environment_id},
                {"environments.environment_id": environment_id}
            ]
        })
    
    async def update_project_environment(
        self,
        project_id: str,
        environment_id: str,
        set_as_default: bool = False
    ) -> Optional[Project]:
        """
        Update project environment configuration within organization.
        
        Args:
            project_id: Project identifier
            environment_id: Environment identifier
            set_as_default: Whether to set as default environment
            
        Returns:
            Updated project or None if not found
        """
        project = await self.find_by_project_id(project_id)
        if not project:
            return None
        
        if set_as_default:
            project.default_environment_id = environment_id
        
        project.updated_at = datetime.utcnow()
        return await self.update(project)
    
    async def bulk_update_projects(
        self,
        project_updates: List[Dict[str, Any]]
    ) -> List[Project]:
        """
        Bulk update multiple projects within organization.
        
        Args:
            project_updates: List of project update dictionaries
            
        Returns:
            List of updated projects
        """
        updated_projects = []
        
        for update_data in project_updates:
            project_id = update_data.get("project_id")
            if not project_id:
                continue
            
            project = await self.find_by_project_id(project_id)
            if not project:
                continue
            
            # Apply updates
            for field, value in update_data.items():
                if field != "project_id" and hasattr(project, field):
                    setattr(project, field, value)
            
            project.updated_at = datetime.utcnow()
            updated_project = await self.update(project)
            updated_projects.append(updated_project)
        
        return updated_projects
