"""
Multi-Tenant Execution Repository for QAK

Organization-aware repository for Execution entities with automatic organization_id scoping.
Provides specialized queries for test execution analytics and reporting.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from src.repositories.base_repository import MultiTenantRepository
from src.database.models.execution import Execution
from src.exceptions.auth_exceptions import OrganizationAccessDeniedException
import logging

logger = logging.getLogger(__name__)


class ExecutionRepository(MultiTenantRepository[Execution]):
    """Multi-tenant repository for Execution entities."""
    
    def __init__(self):
        """Initialize execution repository."""
        super().__init__(Execution, "organization_id")
        logger.debug("Multi-tenant ExecutionRepository initialized")
    
    async def find_by_execution_id(self, execution_id: str) -> Optional[Execution]:
        """
        Find execution by execution_id within organization context.
        
        Args:
            execution_id: Execution identifier
            
        Returns:
            Execution instance or None if not found in organization
        """
        executions = await self.find_all({"execution_id": execution_id})
        return executions[0] if executions else None
    
    async def find_by_project_id(
        self,
        project_id: str,
        limit: Optional[int] = None
    ) -> List[Execution]:
        """
        Find executions by project_id within organization context.
        
        Args:
            project_id: Project identifier
            limit: Maximum number of executions to return
            
        Returns:
            List of executions for the project
        """
        return await self.find_all(
            filter_dict={"project_id": project_id},
            limit=limit,
            sort=[("started_at", -1)]
        )
    
    async def find_by_status(
        self,
        status: str,
        limit: Optional[int] = None
    ) -> List[Execution]:
        """
        Find executions by status within organization context.
        
        Args:
            status: Execution status
            limit: Maximum number of executions to return
            
        Returns:
            List of executions with the specified status
        """
        return await self.find_all(
            filter_dict={"status": status},
            limit=limit,
            sort=[("started_at", -1)]
        )
    
    async def find_recent_executions(
        self,
        limit: int = 50,
        hours: int = 24
    ) -> List[Execution]:
        """
        Find recent executions within organization context.
        
        Args:
            limit: Maximum number of executions to return
            hours: Number of hours to look back
            
        Returns:
            List of recent executions
        """
        since = datetime.utcnow() - timedelta(hours=hours)
        
        return await self.find_all(
            filter_dict={"started_at": {"$gte": since}},
            limit=limit,
            sort=[("started_at", -1)]
        )
    
    async def find_by_environment(
        self,
        environment_id: str,
        limit: Optional[int] = None
    ) -> List[Execution]:
        """
        Find executions by environment within organization context.
        
        Args:
            environment_id: Environment identifier
            limit: Maximum number of executions to return
            
        Returns:
            List of executions for the environment
        """
        return await self.find_all(
            filter_dict={"environment_id": environment_id},
            limit=limit,
            sort=[("started_at", -1)]
        )
    
    async def find_failed_executions(
        self,
        limit: int = 50,
        days: int = 7
    ) -> List[Execution]:
        """
        Find failed executions within organization context.
        
        Args:
            limit: Maximum number of executions to return
            days: Number of days to look back
            
        Returns:
            List of failed executions
        """
        since = datetime.utcnow() - timedelta(days=days)
        
        return await self.find_all(
            filter_dict={
                "status": {"$in": ["failed", "error", "timeout"]},
                "started_at": {"$gte": since}
            },
            limit=limit,
            sort=[("started_at", -1)]
        )
    
    async def get_execution_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get execution statistics for the current organization.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with execution statistics
        """
        base_stats = await self.get_organization_statistics()
        
        try:
            since = datetime.utcnow() - timedelta(days=days)
            
            # Get executions from the specified period
            recent_executions = await self.find_all({
                "started_at": {"$gte": since}
            })
            
            # Calculate statistics
            total_executions = len(recent_executions)
            
            status_counts = {}
            total_duration = 0
            successful_executions = 0
            failed_executions = 0
            
            for execution in recent_executions:
                # Count by status
                status = execution.status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Duration statistics
                if execution.duration_ms:
                    total_duration += execution.duration_ms
                
                # Success/failure counts
                if status in ["passed", "success", "completed"]:
                    successful_executions += 1
                elif status in ["failed", "error", "timeout"]:
                    failed_executions += 1
            
            # Calculate success rate
            success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0
            
            # Calculate average duration
            avg_duration_ms = total_duration / total_executions if total_executions > 0 else 0
            
            base_stats.update({
                "period_days": days,
                "total_executions": total_executions,
                "successful_executions": successful_executions,
                "failed_executions": failed_executions,
                "success_rate_percent": round(success_rate, 2),
                "status_breakdown": status_counts,
                "avg_duration_ms": round(avg_duration_ms, 2),
                "avg_duration_seconds": round(avg_duration_ms / 1000, 2) if avg_duration_ms > 0 else 0,
            })
            
            return base_stats
            
        except Exception as e:
            logger.error(f"Failed to get execution statistics: {e}")
            base_stats["error"] = str(e)
            return base_stats
    
    async def get_project_execution_summary(
        self,
        project_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get execution summary for a specific project within organization.
        
        Args:
            project_id: Project identifier
            days: Number of days to analyze
            
        Returns:
            Dictionary with project execution summary
        """
        try:
            since = datetime.utcnow() - timedelta(days=days)
            
            executions = await self.find_all({
                "project_id": project_id,
                "started_at": {"$gte": since}
            })
            
            if not executions:
                return {
                    "project_id": project_id,
                    "period_days": days,
                    "total_executions": 0,
                    "message": "No executions found for this project in the specified period"
                }
            
            # Calculate summary statistics
            total_executions = len(executions)
            successful = sum(1 for e in executions if e.status in ["passed", "success", "completed"])
            failed = sum(1 for e in executions if e.status in ["failed", "error", "timeout"])
            
            # Get latest execution
            latest_execution = max(executions, key=lambda e: e.started_at)
            
            # Calculate average duration
            durations = [e.duration_ms for e in executions if e.duration_ms]
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            return {
                "project_id": project_id,
                "period_days": days,
                "total_executions": total_executions,
                "successful_executions": successful,
                "failed_executions": failed,
                "success_rate_percent": round((successful / total_executions * 100), 2),
                "latest_execution_date": latest_execution.started_at.isoformat(),
                "latest_execution_status": latest_execution.status,
                "avg_duration_ms": round(avg_duration, 2),
                "avg_duration_seconds": round(avg_duration / 1000, 2) if avg_duration > 0 else 0,
            }
            
        except Exception as e:
            logger.error(f"Failed to get project execution summary: {e}")
            return {
                "project_id": project_id,
                "error": str(e)
            }
    
    async def delete_old_executions(
        self,
        days: int = 90,
        keep_failed: bool = True
    ) -> int:
        """
        Delete old executions within organization context.
        
        Args:
            days: Delete executions older than this many days
            keep_failed: Whether to keep failed executions
            
        Returns:
            Number of executions deleted
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Build deletion criteria
        delete_criteria = {"started_at": {"$lt": cutoff_date}}
        
        if keep_failed:
            delete_criteria["status"] = {"$nin": ["failed", "error", "timeout"]}
        
        # Find executions to delete
        executions_to_delete = await self.find_all(delete_criteria)
        
        # Delete executions
        deleted_count = 0
        for execution in executions_to_delete:
            if await self.delete(execution):
                deleted_count += 1
        
        logger.info(f"Deleted {deleted_count} old executions from organization")
        return deleted_count
    
    async def search_executions(
        self,
        search_term: str,
        limit: int = 50
    ) -> List[Execution]:
        """
        Search executions by various fields within organization.
        
        Args:
            search_term: Term to search for
            limit: Maximum number of results
            
        Returns:
            List of matching executions
        """
        return await self.find_all(
            filter_dict={
                "$or": [
                    {"execution_id": {"$regex": search_term, "$options": "i"}},
                    {"project_id": {"$regex": search_term, "$options": "i"}},
                    {"test_case_id": {"$regex": search_term, "$options": "i"}},
                    {"environment_name": {"$regex": search_term, "$options": "i"}},
                ]
            },
            limit=limit,
            sort=[("started_at", -1)]
        )
