"""
Base Repository Classes for QAK Multi-Tenant Architecture

Provides organization-aware repository base classes with automatic organization_id scoping.
Implements the repository pattern for multi-tenant data access.
"""

from typing import TypeVar, Generic, List, Optional, Dict, Any, Union
from abc import ABC, abstractmethod
from datetime import datetime
from beanie import Document
from pymongo import UpdateOne, InsertOne, DeleteOne
from src.exceptions.auth_exceptions import OrganizationAccessDeniedException
import logging

logger = logging.getLogger(__name__)

# Type variable for document types
DocumentType = TypeVar('DocumentType', bound=Document)


class BaseRepository(Generic[DocumentType], ABC):
    """Base repository class for common database operations."""
    
    def __init__(self, document_class: type[DocumentType]):
        """
        Initialize repository with document class.
        
        Args:
            document_class: Beanie document class
        """
        self.document_class = document_class
        self.collection_name = document_class.get_collection_name()
        logger.debug(f"Repository initialized for {self.collection_name}")
    
    async def find_by_id(self, document_id: str) -> Optional[DocumentType]:
        """
        Find document by ID.
        
        Args:
            document_id: Document identifier
            
        Returns:
            Document instance or None if not found
        """
        try:
            return await self.document_class.get(document_id)
        except Exception as e:
            logger.debug(f"Document not found by ID {document_id}: {e}")
            return None
    
    async def find_all(
        self,
        filter_dict: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[tuple]] = None
    ) -> List[DocumentType]:
        """
        Find all documents matching criteria.
        
        Args:
            filter_dict: MongoDB filter dictionary
            limit: Maximum number of documents to return
            skip: Number of documents to skip
            sort: Sort criteria as list of (field, direction) tuples
            
        Returns:
            List of matching documents
        """
        query = self.document_class.find(filter_dict or {})
        
        if sort:
            query = query.sort(sort)
        if skip:
            query = query.skip(skip)
        if limit:
            query = query.limit(limit)
        
        return await query.to_list()
    
    async def count(self, filter_dict: Optional[Dict[str, Any]] = None) -> int:
        """
        Count documents matching criteria.
        
        Args:
            filter_dict: MongoDB filter dictionary
            
        Returns:
            Number of matching documents
        """
        return await self.document_class.find(filter_dict or {}).count()
    
    async def create(self, document: DocumentType) -> DocumentType:
        """
        Create a new document.
        
        Args:
            document: Document instance to create
            
        Returns:
            Created document with ID
        """
        await document.insert()
        logger.debug(f"Document created in {self.collection_name}")
        return document
    
    async def update(self, document: DocumentType) -> DocumentType:
        """
        Update an existing document.
        
        Args:
            document: Document instance to update
            
        Returns:
            Updated document
        """
        await document.save()
        logger.debug(f"Document updated in {self.collection_name}")
        return document
    
    async def delete(self, document: DocumentType) -> bool:
        """
        Delete a document.
        
        Args:
            document: Document instance to delete
            
        Returns:
            True if deleted successfully
        """
        try:
            await document.delete()
            logger.debug(f"Document deleted from {self.collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete document: {e}")
            return False
    
    async def delete_by_id(self, document_id: str) -> bool:
        """
        Delete a document by ID.
        
        Args:
            document_id: Document identifier
            
        Returns:
            True if deleted successfully
        """
        try:
            document = await self.find_by_id(document_id)
            if document:
                return await self.delete(document)
            return False
        except Exception as e:
            logger.error(f"Failed to delete document by ID: {e}")
            return False
    
    async def bulk_create(self, documents: List[DocumentType]) -> List[DocumentType]:
        """
        Create multiple documents in bulk.
        
        Args:
            documents: List of document instances to create
            
        Returns:
            List of created documents
        """
        if not documents:
            return []
        
        await self.document_class.insert_many(documents)
        logger.debug(f"Bulk created {len(documents)} documents in {self.collection_name}")
        return documents
    
    async def exists(self, filter_dict: Dict[str, Any]) -> bool:
        """
        Check if a document exists matching criteria.
        
        Args:
            filter_dict: MongoDB filter dictionary
            
        Returns:
            True if document exists, False otherwise
        """
        count = await self.document_class.find(filter_dict).limit(1).count()
        return count > 0


class MultiTenantRepository(BaseRepository[DocumentType]):
    """Multi-tenant repository with automatic organization_id scoping."""
    
    def __init__(self, document_class: type[DocumentType], organization_field: str = "organization_id"):
        """
        Initialize multi-tenant repository.
        
        Args:
            document_class: Beanie document class
            organization_field: Field name for organization identifier
        """
        super().__init__(document_class)
        self.organization_field = organization_field
        self._current_organization_id: Optional[str] = None
        logger.debug(f"Multi-tenant repository initialized for {self.collection_name}")
    
    def set_organization_context(self, organization_id: str):
        """
        Set the current organization context for all operations.
        
        Args:
            organization_id: Organization identifier
        """
        self._current_organization_id = organization_id
        logger.debug(f"Organization context set to {organization_id} for {self.collection_name}")
    
    def clear_organization_context(self):
        """Clear the current organization context."""
        self._current_organization_id = None
        logger.debug(f"Organization context cleared for {self.collection_name}")
    
    def _ensure_organization_context(self) -> str:
        """
        Ensure organization context is set.
        
        Returns:
            Current organization ID
            
        Raises:
            OrganizationAccessDeniedException: If no organization context is set
        """
        if not self._current_organization_id:
            raise OrganizationAccessDeniedException("No organization context set")
        return self._current_organization_id
    
    def _add_organization_filter(self, filter_dict: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Add organization filter to query.
        
        Args:
            filter_dict: Existing filter dictionary
            
        Returns:
            Filter dictionary with organization constraint
        """
        organization_id = self._ensure_organization_context()
        
        if filter_dict is None:
            filter_dict = {}
        else:
            filter_dict = filter_dict.copy()
        
        filter_dict[self.organization_field] = organization_id
        return filter_dict
    
    def _add_organization_to_document(self, document: DocumentType) -> DocumentType:
        """
        Add organization ID to document before saving.
        
        Args:
            document: Document instance
            
        Returns:
            Document with organization ID set
        """
        organization_id = self._ensure_organization_context()
        setattr(document, self.organization_field, organization_id)
        return document
    
    async def find_by_id(self, document_id: str) -> Optional[DocumentType]:
        """
        Find document by ID within organization context.
        
        Args:
            document_id: Document identifier
            
        Returns:
            Document instance or None if not found in organization
        """
        try:
            document = await self.document_class.get(document_id)
            if document and getattr(document, self.organization_field) == self._current_organization_id:
                return document
            return None
        except Exception as e:
            logger.debug(f"Document not found by ID {document_id} in organization context: {e}")
            return None
    
    async def find_all(
        self,
        filter_dict: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[tuple]] = None
    ) -> List[DocumentType]:
        """
        Find all documents within organization context.
        
        Args:
            filter_dict: MongoDB filter dictionary
            limit: Maximum number of documents to return
            skip: Number of documents to skip
            sort: Sort criteria as list of (field, direction) tuples
            
        Returns:
            List of matching documents in organization
        """
        org_filter = self._add_organization_filter(filter_dict)
        return await super().find_all(org_filter, limit, skip, sort)
    
    async def count(self, filter_dict: Optional[Dict[str, Any]] = None) -> int:
        """
        Count documents within organization context.
        
        Args:
            filter_dict: MongoDB filter dictionary
            
        Returns:
            Number of matching documents in organization
        """
        org_filter = self._add_organization_filter(filter_dict)
        return await super().count(org_filter)
    
    async def create(self, document: DocumentType) -> DocumentType:
        """
        Create document within organization context.
        
        Args:
            document: Document instance to create
            
        Returns:
            Created document with organization ID
        """
        document = self._add_organization_to_document(document)
        return await super().create(document)
    
    async def bulk_create(self, documents: List[DocumentType]) -> List[DocumentType]:
        """
        Create multiple documents within organization context.
        
        Args:
            documents: List of document instances to create
            
        Returns:
            List of created documents with organization ID
        """
        if not documents:
            return []
        
        # Add organization ID to all documents
        org_documents = [self._add_organization_to_document(doc) for doc in documents]
        return await super().bulk_create(org_documents)
    
    async def exists(self, filter_dict: Dict[str, Any]) -> bool:
        """
        Check if document exists within organization context.
        
        Args:
            filter_dict: MongoDB filter dictionary
            
        Returns:
            True if document exists in organization, False otherwise
        """
        org_filter = self._add_organization_filter(filter_dict)
        return await super().exists(org_filter)
    
    async def find_across_organizations(
        self,
        filter_dict: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[tuple]] = None
    ) -> List[DocumentType]:
        """
        Find documents across all organizations (admin operation).
        
        Args:
            filter_dict: MongoDB filter dictionary
            limit: Maximum number of documents to return
            skip: Number of documents to skip
            sort: Sort criteria as list of (field, direction) tuples
            
        Returns:
            List of matching documents across all organizations
        """
        logger.warning(f"Cross-organization query executed on {self.collection_name}")
        return await super().find_all(filter_dict, limit, skip, sort)
    
    async def get_organization_statistics(self) -> Dict[str, Any]:
        """
        Get statistics for the current organization.
        
        Returns:
            Dictionary with organization statistics
        """
        organization_id = self._ensure_organization_context()
        
        try:
            total_count = await self.count()
            
            # Get creation statistics (last 30 days)
            thirty_days_ago = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            thirty_days_ago = thirty_days_ago.replace(day=thirty_days_ago.day - 30)
            
            recent_count = await self.count({
                "created_at": {"$gte": thirty_days_ago}
            })
            
            return {
                "organization_id": organization_id,
                "collection": self.collection_name,
                "total_documents": total_count,
                "recent_documents": recent_count,
                "statistics_date": datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Failed to get organization statistics: {e}")
            return {
                "organization_id": organization_id,
                "collection": self.collection_name,
                "total_documents": 0,
                "recent_documents": 0,
                "error": str(e),
            }
