"""
Organization Model for QAK Multi-Tenant Architecture

Beanie ODM model for organization management in multi-tenant system.
Each organization represents a separate tenant with isolated data.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from beanie import Document, Indexed
from pydantic import Field, field_validator, ConfigDict
import uuid
import re


def generate_slug(name: str) -> str:
    """
    Generate a URL-safe slug from organization name.
    
    Args:
        name: Organization name
        
    Returns:
        URL-safe slug
    """
    # Convert to lowercase and replace spaces/special chars with hyphens
    slug = re.sub(r'[^\w\s-]', '', name.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    # Remove leading/trailing hyphens
    slug = slug.strip('-')
    
    # Ensure minimum length and add random suffix if needed
    if len(slug) < 3:
        slug = f"org-{str(uuid.uuid4())[:8]}"
    
    return slug


class Organization(Document):
    """Organization document model for multi-tenant architecture."""
    
    # Organization identification - indexed for performance
    organization_id: Indexed(str, unique=True) = Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Basic information
    name: Indexed(str, unique=True) = Field(..., description="Organization name")
    slug: Indexed(str, unique=True) = Field(..., description="URL-safe organization identifier")
    description: Optional[str] = Field(default="", description="Organization description")
    
    # Organization settings and configuration
    settings: Dict[str, Any] = Field(default_factory=dict, description="Organization-specific settings")
    
    # Status and metadata
    is_active: bool = Field(default=True, description="Whether the organization is active")
    
    # Timestamps - indexed for queries
    created_at: Indexed(datetime) = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # MongoDB document settings
    class Settings:
        name = "organizations"
        indexes = [
            "name",  # For organization lookups
            "slug",  # For URL-based lookups
            "organization_id",  # For organization identification
            "created_at",  # For organization creation analytics
            "is_active",  # For filtering active organizations
        ]
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        extra="ignore"
    )
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate organization name."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Organization name cannot be empty")
        
        # Check length constraints
        name = v.strip()
        if len(name) < 2:
            raise ValueError("Organization name must be at least 2 characters long")
        if len(name) > 100:
            raise ValueError("Organization name cannot exceed 100 characters")
        
        return name
    
    @field_validator('slug')
    @classmethod
    def validate_slug(cls, v):
        """Validate organization slug format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Organization slug cannot be empty")
        
        slug = v.strip().lower()
        
        # Check slug format (alphanumeric and hyphens only)
        if not re.match(r'^[a-z0-9-]+$', slug):
            raise ValueError("Slug can only contain lowercase letters, numbers, and hyphens")
        
        # Check length constraints
        if len(slug) < 2:
            raise ValueError("Slug must be at least 2 characters long")
        if len(slug) > 50:
            raise ValueError("Slug cannot exceed 50 characters")
        
        # Cannot start or end with hyphen
        if slug.startswith('-') or slug.endswith('-'):
            raise ValueError("Slug cannot start or end with a hyphen")
        
        return slug
    
    @classmethod
    def create_with_auto_slug(cls, name: str, **kwargs) -> "Organization":
        """
        Create organization with automatically generated slug.
        
        Args:
            name: Organization name
            **kwargs: Additional organization fields
            
        Returns:
            Organization instance with auto-generated slug
        """
        slug = generate_slug(name)
        return cls(name=name, slug=slug, **kwargs)
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    def deactivate(self):
        """Deactivate the organization."""
        self.is_active = False
        self.update_timestamp()
    
    def activate(self):
        """Activate the organization."""
        self.is_active = True
        self.update_timestamp()
    
    def update_settings(self, new_settings: Dict[str, Any]):
        """Update organization settings."""
        self.settings.update(new_settings)
        self.update_timestamp()
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific organization setting."""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """Set a specific organization setting."""
        self.settings[key] = value
        self.update_timestamp()
    
    @property
    def display_name(self) -> str:
        """Get display-friendly name for the organization."""
        return self.name
    
    @property
    def url_identifier(self) -> str:
        """Get URL identifier for the organization."""
        return self.slug
    
    def to_dict(self) -> dict:
        """Convert organization to dictionary for API responses."""
        return {
            "organization_id": self.organization_id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "is_active": self.is_active,
            "settings": self.settings,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
