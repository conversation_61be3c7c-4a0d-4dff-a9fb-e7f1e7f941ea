"""
User Model for QAK Multi-Tenant Authentication

Beanie ODM model for user authentication and management.
Supports multi-tenant architecture with organization-based access control.
"""

from typing import Optional
from datetime import datetime
from beanie import Document, Indexed
from pydantic import Field, field_validator, EmailStr, ConfigDict
import uuid


class User(Document):
    """User document model for authentication and profile management."""
    
    # User identification - indexed for performance
    user_id: Indexed(str, unique=True) = Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Authentication fields
    email: Indexed(EmailStr, unique=True) = Field(..., description="User email address")
    password_hash: str = Field(..., description="Bcrypt hashed password")
    
    # Profile information
    first_name: str = Field(..., description="User's first name")
    last_name: str = Field(..., description="User's last name")
    
    # Account status and verification
    is_active: bool = Field(default=True, description="Whether the user account is active")
    email_verified: bool = Field(default=False, description="Whether the user's email is verified")
    
    # Timestamps - indexed for queries
    created_at: Indexed(datetime) = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    
    # MongoDB document settings
    class Settings:
        name = "users"
        indexes = [
            "email",  # For login lookups
            "user_id",  # For user identification
            "created_at",  # For user registration analytics
            "last_login",  # For activity tracking
            "is_active",  # For filtering active users
        ]
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        extra="ignore"
    )
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format and normalize."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Email cannot be empty")
        return v.lower().strip()
    
    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v):
        """Validate name fields."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Name fields cannot be empty")
        return v.strip()
    
    @field_validator('password_hash')
    @classmethod
    def validate_password_hash(cls, v):
        """Validate password hash format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Password hash cannot be empty")
        # Basic bcrypt hash format validation
        if not v.startswith('$2b$'):
            raise ValueError("Invalid password hash format")
        return v
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    def update_last_login(self):
        """Update the last_login timestamp."""
        self.last_login = datetime.utcnow()
        self.update_timestamp()
    
    def deactivate(self):
        """Deactivate the user account."""
        self.is_active = False
        self.update_timestamp()
    
    def activate(self):
        """Activate the user account."""
        self.is_active = True
        self.update_timestamp()
    
    def verify_email(self):
        """Mark the user's email as verified."""
        self.email_verified = True
        self.update_timestamp()
    
    @property
    def full_name(self) -> str:
        """Get the user's full name."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def display_name(self) -> str:
        """Get a display-friendly name for the user."""
        return self.full_name
    
    @property
    def is_verified_and_active(self) -> bool:
        """Check if user is both verified and active."""
        return self.is_active and self.email_verified
    
    def to_profile_dict(self) -> dict:
        """Convert user to profile dictionary for API responses."""
        return {
            "user_id": self.user_id,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": self.full_name,
            "is_active": self.is_active,
            "email_verified": self.email_verified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
        }
