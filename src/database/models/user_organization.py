"""
UserOrganization Model for QAK Multi-Tenant RBAC

Beanie ODM model for managing user-organization relationships and roles.
Implements role-based access control (RBAC) for multi-tenant architecture.
"""

from typing import Optional
from datetime import datetime
from enum import Enum
from beanie import Document, Indexed
from pydantic import Field, field_validator, ConfigDict
import uuid


class Role(str, Enum):
    """User roles within an organization."""
    USER = "USER"                    # Regular user with basic permissions
    ORG_ADMIN = "ORG_ADMIN"         # Organization administrator
    ADMIN = "ADMIN"                  # System administrator (super user)


class UserOrganization(Document):
    """User-Organization relationship model with role-based access control."""
    
    # Relationship identification
    relationship_id: Indexed(str, unique=True) = Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Foreign key references - compound indexed for performance
    user_id: Indexed(str) = Field(..., description="Reference to User document")
    organization_id: Indexed(str) = Field(..., description="Reference to Organization document")
    
    # Role and permissions
    role: Indexed(str) = Field(default=Role.USER.value, description="User role within the organization")
    
    # Status and metadata
    is_active: bool = Field(default=True, description="Whether the relationship is active")
    
    # Timestamps - indexed for queries
    joined_at: Indexed(datetime) = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_accessed: Optional[datetime] = None
    
    # MongoDB document settings
    class Settings:
        name = "user_organizations"
        indexes = [
            # Compound index for efficient user-organization lookups
            [("user_id", 1), ("organization_id", 1)],
            # Individual indexes for common queries
            "user_id",
            "organization_id", 
            "role",
            "joined_at",
            "is_active",
            # Compound index for role-based queries within organizations
            [("organization_id", 1), ("role", 1)],
            # Compound index for active relationships
            [("user_id", 1), ("is_active", 1)],
        ]
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        extra="ignore"
    )
    
    @field_validator('role', mode='before')
    @classmethod
    def validate_role(cls, v):
        """Validate and convert role to enum value."""
        if isinstance(v, str):
            try:
                return Role(v).value
            except ValueError:
                raise ValueError(f"Invalid role: {v}. Must be one of: {[r.value for r in Role]}")
        elif isinstance(v, Role):
            return v.value
        return v
    
    @field_validator('user_id', 'organization_id')
    @classmethod
    def validate_ids(cls, v):
        """Validate ID fields."""
        if not v or len(v.strip()) == 0:
            raise ValueError("ID fields cannot be empty")
        return v.strip()
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    def update_last_accessed(self):
        """Update the last_accessed timestamp."""
        self.last_accessed = datetime.utcnow()
        self.update_timestamp()
    
    def change_role(self, new_role: Role):
        """Change the user's role within the organization."""
        self.role = new_role.value
        self.update_timestamp()
    
    def deactivate(self):
        """Deactivate the user-organization relationship."""
        self.is_active = False
        self.update_timestamp()
    
    def activate(self):
        """Activate the user-organization relationship."""
        self.is_active = True
        self.update_timestamp()
    
    @property
    def role_enum(self) -> Role:
        """Get the role as an enum."""
        return Role(self.role)
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == Role.ADMIN.value
    
    @property
    def is_org_admin(self) -> bool:
        """Check if user has organization admin role."""
        return self.role == Role.ORG_ADMIN.value
    
    @property
    def is_user(self) -> bool:
        """Check if user has regular user role."""
        return self.role == Role.USER.value
    
    @property
    def has_admin_privileges(self) -> bool:
        """Check if user has admin or org_admin privileges."""
        return self.role in [Role.ADMIN.value, Role.ORG_ADMIN.value]
    
    @property
    def is_active_relationship(self) -> bool:
        """Check if the relationship is active."""
        return self.is_active
    
    def has_permission(self, permission: str) -> bool:
        """
        Check if the user has a specific permission based on their role.
        
        Args:
            permission: Permission string to check
            
        Returns:
            True if user has permission, False otherwise
        """
        role_permissions = {
            Role.USER.value: [
                "read:projects",
                "create:projects", 
                "update:own_projects",
                "delete:own_projects",
                "read:test_cases",
                "create:test_cases",
                "update:own_test_cases",
                "delete:own_test_cases",
                "execute:tests",
            ],
            Role.ORG_ADMIN.value: [
                # All USER permissions plus:
                "read:all_projects",
                "update:all_projects", 
                "delete:all_projects",
                "read:all_test_cases",
                "update:all_test_cases",
                "delete:all_test_cases",
                "manage:users",
                "manage:organization_settings",
                "read:analytics",
            ],
            Role.ADMIN.value: [
                # All permissions (wildcard)
                "*",
            ]
        }
        
        user_permissions = role_permissions.get(self.role, [])
        
        # Admin has all permissions
        if "*" in user_permissions:
            return True
            
        # Check specific permission
        return permission in user_permissions
    
    def to_dict(self) -> dict:
        """Convert relationship to dictionary for API responses."""
        return {
            "relationship_id": self.relationship_id,
            "user_id": self.user_id,
            "organization_id": self.organization_id,
            "role": self.role,
            "is_active": self.is_active,
            "joined_at": self.joined_at.isoformat() if self.joined_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
        }
