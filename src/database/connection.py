"""
Database Connection Manager for QAK MongoDB Integration

Provides async connection management using Motor with proper lifecycle handling.
"""

import asyncio
import logging
import os
import threading
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ServerSelectionTimeoutError, ConnectionFailure
from .config import DatabaseConfig, get_database_config, DatabaseEnvironment

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages MongoDB connections with proper lifecycle and error handling."""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        """Ensure singleton pattern for database manager."""
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """Initialize database manager with configuration."""
        # Only initialize once
        if hasattr(self, '_initialized'):
            return
            
        self.config = config or get_database_config()
        self._client: Optional[AsyncIOMotorClient] = None
        self._database: Optional[AsyncIOMotorDatabase] = None
        self._is_connected = False
        self._initialized = True
        
    @property
    def is_connected(self) -> bool:
        """Check if database is connected."""
        return self._is_connected
    
    @property
    def client(self) -> Optional[AsyncIOMotorClient]:
        """Get the MongoDB client."""
        return self._client
    
    @property
    def database(self) -> Optional[AsyncIOMotorDatabase]:
        """Get the MongoDB database."""
        return self._database
    
    async def connect(self) -> bool:
        """Connect to MongoDB database."""
        async with await self._get_lock():
            if self._is_connected and self._client and self._database:
                return True
                
            return await self._connect_internal()
    
    async def disconnect(self):
        """Disconnect from MongoDB database."""
        async with await self._get_lock():
            if not self._is_connected:
                return
                
            logger.info("🔌 Disconnecting from MongoDB")
            await self._cleanup_internal()
            logger.info("✅ Disconnected from MongoDB")
    
    async def _cleanup(self):
        """Clean up database connections (legacy method for compatibility)."""
        await self._cleanup_internal()
    
    async def ensure_connected(self) -> bool:
        """Ensure database connection is active."""
        async with await self._get_lock():
            if not self._is_connected:
                return await self._connect_internal()
                
            # Test existing connection with timeout
            try:
                if self._client is None or self._database is None:
                    logger.debug("Client or database is None, reconnecting")
                    return await self._connect_internal()
                    
                # Quick ping test with timeout
                await asyncio.wait_for(
                    self._client.admin.command('ping'), 
                    timeout=2.0
                )
                return True
                
            except asyncio.TimeoutError:
                logger.warning("⚠️ Connection ping timeout, reconnecting")
                await self._cleanup_internal()
                return await self._connect_internal()
            except Exception as e:
                logger.warning(f"⚠️ Connection test failed, reconnecting: {e}")
                await self._cleanup_internal()
                return await self._connect_internal()
    
    async def _connect_internal(self) -> bool:
        """Internal connect method without lock (caller must hold lock)."""
        try:
            if self._is_connected and self._client and self._database:
                return True
            logger.info(f"🔌 Connecting to MongoDB")
            
            # Apply environment-specific configuration
            env_config = self.config.get_environment_config()
            connection_options = self.config.connection_options.copy()
            connection_options.update(env_config)
            
            # Create new client with environment-specific options
            self._client = AsyncIOMotorClient(
                self.config.connection_string,
                **connection_options
            )
            
            # Test connection with environment-appropriate timeout
            # Use serverSelectionTimeoutMS from config, convert to seconds for asyncio.wait_for
            timeout_ms = connection_options.get('serverSelectionTimeoutMS', self.config.server_selection_timeout)
            timeout_seconds = timeout_ms / 1000.0
            
            logger.info(f"🔍 Testing connection with {timeout_seconds}s timeout for {self.config.environment.value} environment")
            
            await asyncio.wait_for(
                self._client.admin.command('ping'),
                timeout=timeout_seconds
            )
            
            # Get database
            self._database = self._client[self.config.database_name]
            
            # Optionally log server version (can be slow on Atlas clusters)
            version = "unknown"
            if self.config.environment != DatabaseEnvironment.PRODUCTION or os.getenv("DB_SHOW_VERSION", "false").lower() == "true":
                try:
                    server_info = await self._client.server_info()
                    version = server_info.get("version", "unknown")
                except Exception:
                    # Non-critical, ignore errors
                    pass
            
            self._is_connected = True
            
            logger.info(f"✅ Connected to MongoDB database: {self.config.database_name}")
            if version != "unknown":
                logger.info(f"📊 MongoDB Server Version: {version}")
            
            return True
            
        except asyncio.TimeoutError:
            logger.error("❌ MongoDB connection timeout")
            await self._cleanup_internal()
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error connecting to MongoDB: {e}")
            await self._cleanup_internal()
            return False
    
    async def _cleanup_internal(self):
        """Internal cleanup method without lock (caller must hold lock)."""
        if self._client:
            try:
                self._client.close()
            except Exception as e:
                logger.debug(f"Error closing client: {e}")
            
        self._client = None
        self._database = None
        self._is_connected = False
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics for monitoring."""
        if not self._is_connected:
            return {"status": "disconnected"}
            
        try:
            stats = await self._database.command("dbStats")
            return {
                "status": "connected",
                "database": self.config.database_name,
                "collections": stats.get("collections", 0),
                "objects": stats.get("objects", 0),
                "data_size": stats.get("dataSize", 0),
                "storage_size": stats.get("storageSize", 0),
                "indexes": stats.get("indexes", 0),
                "index_size": stats.get("indexSize", 0),
            }
        except Exception as e:
            logger.error(f"❌ Failed to get database stats: {e}")
            return {"status": "error", "error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check and return status."""
        health_info = {
            "database_connection": False,
            "database_name": self.config.database_name,
            "host": self.config.host,
            "port": self.config.port,
            "environment": self.config.environment.value,
        }
        
        try:
            if await self.ensure_connected():
                health_info["database_connection"] = True
                
                # Get additional health metrics
                stats = await self.get_database_stats()
                health_info.update(stats)
                
                logger.info("✅ Database health check passed")
            else:
                logger.warning("⚠️ Database health check failed - not connected")
                
        except Exception as e:
            logger.error(f"❌ Database health check error: {e}")
            health_info["error"] = str(e)
            
        return health_info
    
    async def create_indexes(self, collection_indexes: Dict[str, list]):
        """Create database indexes for optimal performance."""
        if not self._is_connected:
            logger.error("❌ Cannot create indexes - not connected to database")
            return
            
        try:
            for collection_name, indexes in collection_indexes.items():
                collection = self._database[collection_name]
                
                for index_spec in indexes:
                    if isinstance(index_spec, dict):
                        keys = index_spec.get("keys")
                        options = index_spec.get("options", {})
                        
                        if keys:
                            result = await collection.create_index(keys, **options)
                            logger.info(f"📊 Created index {result} on {collection_name}")
                    else:
                        # Simple index specification
                        result = await collection.create_index(index_spec)
                        logger.info(f"📊 Created index {result} on {collection_name}")
                        
        except Exception as e:
            logger.error(f"❌ Failed to create indexes: {e}")
    
    @asynccontextmanager
    async def get_connection(self):
        """Context manager for database connections."""
        if not await self.ensure_connected():
            raise ConnectionFailure("Unable to establish database connection")
            
        try:
            yield self._database
        except Exception as e:
            logger.error(f"❌ Database operation error: {e}")
            raise
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    async def _get_lock(self):
        """Get or create a lock for the current event loop."""
        try:
            loop = asyncio.get_running_loop()
            lock_attr = f'_connection_lock_{id(loop)}'
            
            if not hasattr(self, lock_attr):
                setattr(self, lock_attr, asyncio.Lock())
            
            return getattr(self, lock_attr)
        except RuntimeError:
            # No running loop, create a basic lock
            if not hasattr(self, '_connection_lock_default'):
                self._connection_lock_default = asyncio.Lock()
            return self._connection_lock_default


# Global database manager instance
_database_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global _database_manager
    
    if _database_manager is None:
        _database_manager = DatabaseManager()
        logger.info("🔧 Database manager initialized")
        
    return _database_manager


async def get_database() -> AsyncIOMotorDatabase:
    """Get the database instance, ensuring connection."""
    manager = get_database_manager()
    
    if not await manager.ensure_connected():
        raise ConnectionFailure("Unable to establish database connection")
        
    return manager.database


async def initialize_database() -> bool:
    """Initialize database connection on application startup."""
    try:
        manager = get_database_manager()
        success = await manager.connect()
        
        if success:
            logger.info("🚀 Database initialization successful")
            logger.info("📊 Database indexes will be managed by Beanie ODM models")
            
        return success
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False


async def shutdown_database():
    """Shutdown database connection on application shutdown."""
    try:
        manager = get_database_manager()
        await manager.disconnect()
        logger.info("👋 Database shutdown completed")
        
    except Exception as e:
        logger.error(f"❌ Database shutdown error: {e}")


# Database health check endpoint
async def database_health_check() -> Dict[str, Any]:
    """Perform database health check for monitoring."""
    manager = get_database_manager()
    return await manager.health_check()


def reset_database_manager():
    """Reset the global database manager (mainly for testing)."""
    global _database_manager
    _database_manager = None 