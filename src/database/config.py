"""
Database Configuration for QAK MongoDB Integration

Provides environment-based configuration with validation and connection settings.
"""

import os
from typing import Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class DatabaseEnvironment(str, Enum):
    """Database environment types."""
    DEVELOPMENT = "development"
    TESTING = "testing"  
    PRODUCTION = "production"


class DatabaseConfig(BaseSettings):
    """Database configuration with environment-based settings."""
    
    # MongoDB Connection Settings
    uri: Optional[str] = Field(default=None, description="Full MongoDB connection URI overrides other settings")
    host: str = Field(default="localhost", description="MongoDB host")
    port: int = Field(default=27017, description="MongoDB port")
    username: Optional[str] = Field(default=None, description="MongoDB username")
    password: Optional[str] = Field(default=None, description="MongoDB password")
    database_name: str = Field(default="qak", description="Database name")
    
    # Connection Pool Settings
    min_pool_size: int = Field(default=1, description="Minimum connection pool size")
    max_pool_size: int = Field(default=10, description="Maximum connection pool size")
    max_idle_time: int = Field(default=30000, description="Max idle time in milliseconds")
    
    # Retry Settings
    retry_writes: bool = Field(default=True, description="Enable retry writes")
    retry_reads: bool = Field(default=True, description="Enable retry reads")
    server_selection_timeout: int = Field(default=5000, description="Server selection timeout in ms")
    
    # Environment Settings
    environment: DatabaseEnvironment = Field(default=DatabaseEnvironment.DEVELOPMENT)
    
    # SSL/TLS Settings
    use_ssl: bool = Field(default=False, description="Use SSL connection")
    ssl_cert_path: Optional[str] = Field(default=None, description="SSL certificate path")
    
    model_config = SettingsConfigDict(
        env_prefix="MONGODB_",
        case_sensitive=False
    )
        
    @field_validator('database_name')
    @classmethod
    def validate_database_name(cls, v, info):
        """Validate and adjust database name based on environment."""
        # In Pydantic v2, we get values from info.data
        env = info.data.get('environment', DatabaseEnvironment.DEVELOPMENT) if info.data else DatabaseEnvironment.DEVELOPMENT
        
        if env == DatabaseEnvironment.TESTING:
            return f"{v}_test"
        elif env == DatabaseEnvironment.DEVELOPMENT:
            return f"{v}_dev"
        
        return v
    
    @property
    def connection_string(self) -> str:
        """Generate MongoDB connection string."""
        if self.uri:
            # If a full connection string is provided, use it directly
            return self.uri
        
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"
        else:
            auth = ""
            
        ssl_params = ""
        if self.use_ssl:
            ssl_params = "&ssl=true"
            if self.ssl_cert_path:
                ssl_params += f"&ssl_cert_file={self.ssl_cert_path}"
        
        return (
            f"mongodb://{auth}{self.host}:{self.port}/{self.database_name}"
            f"?retryWrites={str(self.retry_writes).lower()}"
            f"&retryReads={str(self.retry_reads).lower()}"
            f"&serverSelectionTimeoutMS={self.server_selection_timeout}"
            f"&minPoolSize={self.min_pool_size}"
            f"&maxPoolSize={self.max_pool_size}"
            f"&maxIdleTimeMS={self.max_idle_time}"
            f"{ssl_params}"
        )
    
    @property
    def connection_options(self) -> Dict[str, Any]:
        """Get connection options for Motor client."""
        options = {
            "retryWrites": self.retry_writes,
            "retryReads": self.retry_reads,
            "serverSelectionTimeoutMS": self.server_selection_timeout,
            "minPoolSize": self.min_pool_size,
            "maxPoolSize": self.max_pool_size,
            "maxIdleTimeMS": self.max_idle_time,
        }
        
        # Add SSL configuration for Atlas connections
        if self.uri and "mongodb+srv://" in self.uri:
            # Check if we should allow invalid certificates
            # This can be overridden with MONGODB_SSL_ALLOW_INVALID_CERTS=true for local development
            allow_invalid_certs = os.getenv("MONGODB_SSL_ALLOW_INVALID_CERTS", "true").lower() == "true"
            
            options.update({
                "tls": True,
                "tlsAllowInvalidCertificates": allow_invalid_certs,
            })
            
            # Add additional SSL options if needed
            ssl_ca_file = os.getenv("MONGODB_SSL_CA_FILE")
            if ssl_ca_file:
                options["tlsCAFile"] = ssl_ca_file
            
        return options
    
    def get_environment_config(self) -> Dict[str, Any]:
        """Get environment-specific configuration adjustments."""
        base_config = {}
        
        if self.environment == DatabaseEnvironment.DEVELOPMENT:
            base_config.update({
                "maxPoolSize": 5,  # Smaller pool for development
                "serverSelectionTimeoutMS": 2000,  # Faster timeout for dev
            })
        elif self.environment == DatabaseEnvironment.TESTING:
            base_config.update({
                "maxPoolSize": 3,  # Minimal pool for testing
                "serverSelectionTimeoutMS": 1000,  # Very fast timeout for tests
            })
        elif self.environment == DatabaseEnvironment.PRODUCTION:
            base_config.update({
                "maxPoolSize": 20,  # Larger pool for production
                "serverSelectionTimeoutMS": 10000,  # More patient timeout for prod
            })
            
        return base_config
    
    async def test_connection(self) -> bool:
        """Test database connection."""
        try:
            from motor.motor_asyncio import AsyncIOMotorClient
            
            client = AsyncIOMotorClient(
                self.connection_string,
                **self.connection_options
            )
            
            # Test connection with ping
            await client.admin.command('ping')
            log_target = f"mongodb://...{self.uri.split('@')[-1]}" if self.uri else f"{self.host}:{self.port}"
            logger.info(f"✅ Database connection successful to {log_target}")
            
            client.close()  # This is not an async function
            return True
            
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def get_health_info(self) -> Dict[str, Any]:
        """Get database health information for monitoring."""
        return {
            "host": self.host,
            "port": self.port,
            "database": self.database_name,
            "environment": self.environment.value,
            "pool_settings": {
                "min_pool_size": self.min_pool_size,
                "max_pool_size": self.max_pool_size,
                "max_idle_time": self.max_idle_time,
            },
            "connection_settings": {
                "retry_writes": self.retry_writes,
                "retry_reads": self.retry_reads,
                "server_selection_timeout": self.server_selection_timeout,
            }
        }


# Global configuration instance
_config: Optional[DatabaseConfig] = None


def get_database_config() -> DatabaseConfig:
    """Get the global database configuration instance."""
    global _config
    
    if _config is None:
        _config = DatabaseConfig()
        logger.info(f"🔧 Database configuration initialized for {_config.environment.value} environment")
        if _config.uri:
            # Hide credentials when logging
            target = _config.uri.split('@')[-1]
        else:
            target = f"{_config.host}:{_config.port}/{_config.database_name}"
        logger.info(f"🔗 Connection target: {target}")
        
    return _config


def reset_database_config():
    """Reset the global configuration (mainly for testing)."""
    global _config
    _config = None


# Environment detection helpers
def is_development() -> bool:
    """Check if running in development environment."""
    return get_database_config().environment == DatabaseEnvironment.DEVELOPMENT


def is_testing() -> bool:
    """Check if running in testing environment."""
    return get_database_config().environment == DatabaseEnvironment.TESTING


def is_production() -> bool:
    """Check if running in production environment."""
    return get_database_config().environment == DatabaseEnvironment.PRODUCTION 