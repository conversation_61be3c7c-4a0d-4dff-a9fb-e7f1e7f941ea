"""
QAK Exceptions Package

Custom exception classes for the QAK application.
"""

from .auth_exceptions import (
    AuthenticationError,
    InvalidCredentialsException,
    UserAlreadyExistsException,
    InvalidTokenException,
    TokenExpiredException,
    InsufficientPermissionsException,
    EmailAlreadyExistsException,
    OrganizationNotFoundException,
    UserNotFoundException,
    InvalidPasswordException,
    AccountDeactivatedException,
    EmailNotVerifiedException,
    RateLimitExceededException,
)

__all__ = [
    "AuthenticationError",
    "InvalidCredentialsException",
    "UserAlreadyExistsException",
    "InvalidTokenException",
    "TokenExpiredException",
    "InsufficientPermissionsException",
    "EmailAlreadyExistsException",
    "OrganizationNotFoundException",
    "UserNotFoundException",
    "InvalidPasswordException",
    "AccountDeactivatedException",
    "EmailNotVerifiedException",
    "RateLimitExceededException",
]
