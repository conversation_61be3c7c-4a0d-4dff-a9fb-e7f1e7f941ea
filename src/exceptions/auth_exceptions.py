"""
Authentication Exception Classes for QAK

Custom exception classes for authentication and authorization errors.
Provides detailed error information for proper API error responses.
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status


class AuthenticationError(HTTPException):
    """Base authentication error class."""
    
    def __init__(
        self,
        detail: str = "Authentication failed",
        status_code: int = status.HTTP_401_UNAUTHORIZED,
        headers: Optional[Dict[str, str]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)


class InvalidCredentialsException(AuthenticationError):
    """Exception raised when user provides invalid credentials."""
    
    def __init__(self, detail: str = "Invalid email or password"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"}
        )


class UserAlreadyExistsException(HTTPException):
    """Exception raised when attempting to create a user that already exists."""
    
    def __init__(self, email: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"User with email '{email}' already exists"
        )


class EmailAlreadyExistsException(HTTPException):
    """Exception raised when email is already registered."""
    
    def __init__(self, email: str):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Email '{email}' is already registered"
        )


class InvalidTokenException(AuthenticationError):
    """Exception raised when JWT token is invalid."""
    
    def __init__(self, detail: str = "Invalid or malformed token"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"}
        )


class TokenExpiredException(AuthenticationError):
    """Exception raised when JWT token has expired."""
    
    def __init__(self, detail: str = "Token has expired"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"}
        )


class InsufficientPermissionsException(HTTPException):
    """Exception raised when user lacks required permissions."""
    
    def __init__(self, required_permission: str = None):
        detail = "Insufficient permissions"
        if required_permission:
            detail = f"Insufficient permissions. Required: {required_permission}"
        
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class UserNotFoundException(HTTPException):
    """Exception raised when user is not found."""
    
    def __init__(self, identifier: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User not found: {identifier}"
        )


class OrganizationNotFoundException(HTTPException):
    """Exception raised when organization is not found."""
    
    def __init__(self, identifier: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Organization not found: {identifier}"
        )


class InvalidPasswordException(HTTPException):
    """Exception raised when password doesn't meet policy requirements."""
    
    def __init__(self, errors: list[str]):
        detail = "Password does not meet policy requirements"
        if errors:
            detail = f"Password validation failed: {', '.join(errors)}"
        
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )


class AccountDeactivatedException(AuthenticationError):
    """Exception raised when user account is deactivated."""
    
    def __init__(self, detail: str = "User account is deactivated"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class EmailNotVerifiedException(AuthenticationError):
    """Exception raised when user email is not verified."""
    
    def __init__(self, detail: str = "Email address is not verified"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class RateLimitExceededException(HTTPException):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, detail: str = "Rate limit exceeded. Please try again later."):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            headers={"Retry-After": "300"}  # 5 minutes
        )


class OrganizationAccessDeniedException(HTTPException):
    """Exception raised when user doesn't have access to organization."""
    
    def __init__(self, organization_id: str):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Access denied to organization: {organization_id}"
        )


class InvalidRefreshTokenException(AuthenticationError):
    """Exception raised when refresh token is invalid or expired."""
    
    def __init__(self, detail: str = "Invalid or expired refresh token"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"}
        )


class TokenBlacklistedException(AuthenticationError):
    """Exception raised when token is blacklisted."""
    
    def __init__(self, detail: str = "Token has been revoked"):
        super().__init__(
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED,
            headers={"WWW-Authenticate": "Bearer"}
        )


class MultipleOrganizationsException(HTTPException):
    """Exception raised when user belongs to multiple organizations but no context is provided."""
    
    def __init__(self, detail: str = "User belongs to multiple organizations. Organization context required."):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )


class PasswordResetTokenInvalidException(HTTPException):
    """Exception raised when password reset token is invalid or expired."""
    
    def __init__(self, detail: str = "Invalid or expired password reset token"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )


class EmailVerificationTokenInvalidException(HTTPException):
    """Exception raised when email verification token is invalid or expired."""
    
    def __init__(self, detail: str = "Invalid or expired email verification token"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail
        )


# Utility function to create standardized error responses
def create_auth_error_response(
    error_type: str,
    message: str,
    details: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a standardized authentication error response.
    
    Args:
        error_type: Type of error (e.g., "invalid_credentials")
        message: Human-readable error message
        details: Additional error details
        
    Returns:
        Standardized error response dictionary
    """
    response = {
        "success": False,
        "error": error_type,
        "message": message
    }
    
    if details:
        response["details"] = details
    
    return response
