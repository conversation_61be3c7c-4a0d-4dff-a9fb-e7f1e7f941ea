"""
Authentication Request/Response Models for QAK API

Pydantic models for authentication endpoints with comprehensive validation.
Supports multi-tenant authentication with organization context.
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, field_validator
from enum import Enum


class UserRegistrationRequest(BaseModel):
    """Request model for user registration."""
    
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, max_length=128, description="User password")
    first_name: str = Field(..., min_length=1, max_length=50, description="User's first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="User's last name")
    organization_name: str = Field(..., min_length=2, max_length=100, description="Organization name")
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate and normalize email."""
        return v.lower().strip()
    
    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v):
        """Validate name fields."""
        return v.strip()
    
    @field_validator('organization_name')
    @classmethod
    def validate_organization_name(cls, v):
        """Validate organization name."""
        return v.strip()
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v):
        """Basic password validation."""
        if len(v.strip()) < 8:
            raise ValueError("Password must be at least 8 characters long")
        return v


class LoginRequest(BaseModel):
    """Request model for user login."""
    
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate and normalize email."""
        return v.lower().strip()


class RefreshTokenRequest(BaseModel):
    """Request model for token refresh."""
    
    refresh_token: str = Field(..., description="Refresh token")


class TokenResponse(BaseModel):
    """Response model for token-related endpoints."""
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration in seconds")


class UserProfileResponse(BaseModel):
    """Response model for user profile information."""
    
    user_id: str = Field(..., description="User identifier")
    email: str = Field(..., description="User email address")
    first_name: str = Field(..., description="User's first name")
    last_name: str = Field(..., description="User's last name")
    full_name: str = Field(..., description="User's full name")
    is_active: bool = Field(..., description="Whether user account is active")
    email_verified: bool = Field(..., description="Whether user's email is verified")
    created_at: str = Field(..., description="Account creation timestamp")
    last_login: Optional[str] = Field(None, description="Last login timestamp")


class OrganizationResponse(BaseModel):
    """Response model for organization information."""
    
    organization_id: str = Field(..., description="Organization identifier")
    name: str = Field(..., description="Organization name")
    slug: str = Field(..., description="Organization URL slug")
    description: Optional[str] = Field(None, description="Organization description")
    is_active: bool = Field(..., description="Whether organization is active")
    role: str = Field(..., description="User's role in this organization")
    joined_at: str = Field(..., description="When user joined the organization")


class LoginResponse(BaseModel):
    """Response model for successful login."""
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration in seconds")
    user: UserProfileResponse = Field(..., description="User profile information")
    organization: OrganizationResponse = Field(..., description="Current organization context")


class RefreshTokenResponse(BaseModel):
    """Response model for token refresh."""
    
    access_token: str = Field(..., description="New JWT access token")
    refresh_token: str = Field(..., description="New JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Access token expiration in seconds")


class UserMeResponse(BaseModel):
    """Response model for /auth/me endpoint."""
    
    user: UserProfileResponse = Field(..., description="User profile information")
    organization: OrganizationResponse = Field(..., description="Current organization context")
    permissions: list[str] = Field(default_factory=list, description="User permissions in current organization")


class LogoutRequest(BaseModel):
    """Request model for user logout."""
    
    refresh_token: Optional[str] = Field(None, description="Refresh token to blacklist")


class PasswordChangeRequest(BaseModel):
    """Request model for password change."""
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        """Basic password validation."""
        if len(v.strip()) < 8:
            raise ValueError("Password must be at least 8 characters long")
        return v


class EmailVerificationRequest(BaseModel):
    """Request model for email verification."""
    
    verification_token: str = Field(..., description="Email verification token")


class PasswordResetRequest(BaseModel):
    """Request model for password reset."""
    
    email: EmailStr = Field(..., description="User email address")
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate and normalize email."""
        return v.lower().strip()


class PasswordResetConfirmRequest(BaseModel):
    """Request model for password reset confirmation."""
    
    reset_token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, max_length=128, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v):
        """Basic password validation."""
        if len(v.strip()) < 8:
            raise ValueError("Password must be at least 8 characters long")
        return v


class AuthErrorResponse(BaseModel):
    """Response model for authentication errors."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class PasswordStrengthResponse(BaseModel):
    """Response model for password strength validation."""
    
    score: int = Field(..., description="Password strength score (0-100)")
    is_valid: bool = Field(..., description="Whether password meets policy requirements")
    is_strong: bool = Field(..., description="Whether password is considered strong")
    errors: list[str] = Field(default_factory=list, description="Validation errors")
    suggestions: list[str] = Field(default_factory=list, description="Improvement suggestions")


# Common response models for API consistency
class SuccessResponse(BaseModel):
    """Generic success response."""
    
    success: bool = Field(default=True, description="Operation success status")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional response data")


class ErrorResponse(BaseModel):
    """Generic error response."""
    
    success: bool = Field(default=False, description="Operation success status")
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
