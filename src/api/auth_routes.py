"""
Authentication Routes for QAK API

FastAPI routes for user authentication, registration, and token management.
Supports multi-tenant authentication with organization context.
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from src.services.auth.auth_service import AuthService
from src.models.auth_models import (
    UserRegistrationRequest, LoginRequest, LoginResponse,
    RefreshTokenRequest, RefreshTokenResponse, UserMeResponse,
    LogoutRequest, SuccessResponse, ErrorResponse,
    PasswordStrengthResponse
)
from src.exceptions.auth_exceptions import (
    InvalidCredentialsException, UserAlreadyExistsException,
    InvalidTokenException, TokenExpiredException,
    InvalidRefreshTokenException, TokenBlacklistedException,
    create_auth_error_response
)
import logging

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["Authentication"])

# Security scheme
security = HTTPBearer()

# Initialize auth service
auth_service = AuthService()


def get_token_from_header(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Extract token from Authorization header."""
    return credentials.credentials


@router.post(
    "/auth/register",
    response_model=LoginResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register new user",
    description="Register a new user and create their organization. Returns authentication tokens."
)
async def register_user(registration_data: UserRegistrationRequest):
    """
    Register a new user with organization creation.
    
    - **email**: Valid email address (will be normalized)
    - **password**: Password meeting policy requirements
    - **first_name**: User's first name
    - **last_name**: User's last name  
    - **organization_name**: Name for the new organization
    
    Returns authentication tokens and user/organization information.
    """
    try:
        logger.info(f"Registration attempt for email: {registration_data.email}")
        
        response = await auth_service.register_user(registration_data)
        
        logger.info(f"User registered successfully: {registration_data.email}")
        return response
        
    except UserAlreadyExistsException as e:
        logger.warning(f"Registration failed - user exists: {registration_data.email}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e.detail)
        )
    except ValueError as e:
        logger.warning(f"Registration failed - validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Registration failed - unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed. Please try again."
        )


@router.post(
    "/auth/login",
    response_model=LoginResponse,
    summary="User login",
    description="Authenticate user with email and password. Returns authentication tokens."
)
async def login_user(login_data: LoginRequest):
    """
    Authenticate user with email and password.
    
    - **email**: User's email address
    - **password**: User's password
    
    Returns authentication tokens and user/organization information.
    """
    try:
        logger.info(f"Login attempt for email: {login_data.email}")
        
        response = await auth_service.authenticate_user(login_data)
        
        logger.info(f"User logged in successfully: {login_data.email}")
        return response
        
    except InvalidCredentialsException as e:
        logger.warning(f"Login failed - invalid credentials: {login_data.email}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e.detail),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Login failed - unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed. Please try again."
        )


@router.post(
    "/auth/refresh",
    response_model=RefreshTokenResponse,
    summary="Refresh access token",
    description="Get new access token using refresh token."
)
async def refresh_token(refresh_data: RefreshTokenRequest):
    """
    Refresh access token using refresh token.
    
    - **refresh_token**: Valid refresh token
    
    Returns new access and refresh tokens.
    """
    try:
        logger.info("Token refresh attempt")
        
        response = await auth_service.refresh_tokens(refresh_data.refresh_token)
        
        logger.info("Tokens refreshed successfully")
        return response
        
    except (InvalidRefreshTokenException, TokenBlacklistedException) as e:
        logger.warning(f"Token refresh failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e.detail),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Token refresh failed - unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed. Please try again."
        )


@router.post(
    "/auth/logout",
    response_model=SuccessResponse,
    summary="User logout",
    description="Logout user by blacklisting their tokens."
)
async def logout_user(
    logout_data: LogoutRequest,
    token: str = Depends(get_token_from_header)
):
    """
    Logout user by blacklisting tokens.
    
    - **refresh_token**: Optional refresh token to blacklist
    
    Requires valid access token in Authorization header.
    """
    try:
        logger.info("Logout attempt")
        
        success = await auth_service.logout_user(
            access_token=token,
            refresh_token=logout_data.refresh_token
        )
        
        if success:
            logger.info("User logged out successfully")
            return SuccessResponse(message="Logged out successfully")
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Logout failed"
            )
            
    except InvalidTokenException as e:
        logger.warning(f"Logout failed - invalid token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e.detail),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Logout failed - unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed. Please try again."
        )


@router.get(
    "/auth/me",
    response_model=UserMeResponse,
    summary="Get current user",
    description="Get current user information and organization context."
)
async def get_current_user(token: str = Depends(get_token_from_header)):
    """
    Get current user information from access token.
    
    Requires valid access token in Authorization header.
    Returns user profile, organization context, and permissions.
    """
    try:
        logger.info("Get current user attempt")
        
        response = await auth_service.get_current_user(token)
        
        logger.info(f"Current user retrieved: {response.user.user_id}")
        return response
        
    except (InvalidTokenException, TokenBlacklistedException) as e:
        logger.warning(f"Get current user failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e.detail),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Get current user failed - unexpected error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )


@router.post(
    "/auth/password-strength",
    response_model=PasswordStrengthResponse,
    summary="Check password strength",
    description="Validate password strength against policy requirements."
)
async def check_password_strength(password: str):
    """
    Check password strength and get improvement suggestions.
    
    - **password**: Password to validate
    
    Returns strength score, validation status, and suggestions.
    """
    try:
        feedback = auth_service.password_service.get_password_strength_feedback(password)
        
        return PasswordStrengthResponse(
            score=feedback["score"],
            is_valid=feedback["is_valid"],
            is_strong=feedback["is_strong"],
            errors=feedback["errors"],
            suggestions=feedback["suggestions"]
        )
        
    except Exception as e:
        logger.error(f"Password strength check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password strength check failed"
        )
