"""
Role-Based Access Control (RBAC) Service for QAK

Implements comprehensive permission system with role-based access control.
Supports hierarchical permissions and wildcard matching for multi-tenant architecture.
"""

from typing import List, Dict, Set, Optional, Any
from enum import Enum
from src.database.models.user_organization import UserOrganization, Role
from src.database.models.user import User
from src.database.models.organization import Organization
import logging

logger = logging.getLogger(__name__)


class Permission(str, Enum):
    """System permissions for RBAC."""
    
    # Project permissions
    READ_PROJECTS = "read:projects"
    CREATE_PROJECTS = "create:projects"
    UPDATE_OWN_PROJECTS = "update:own_projects"
    UPDATE_ALL_PROJECTS = "update:all_projects"
    DELETE_OWN_PROJECTS = "delete:own_projects"
    DELETE_ALL_PROJECTS = "delete:all_projects"
    
    # Test case permissions
    READ_TEST_CASES = "read:test_cases"
    CREATE_TEST_CASES = "create:test_cases"
    UPDATE_OWN_TEST_CASES = "update:own_test_cases"
    UPDATE_ALL_TEST_CASES = "update:all_test_cases"
    DELETE_OWN_TEST_CASES = "delete:own_test_cases"
    DELETE_ALL_TEST_CASES = "delete:all_test_cases"
    
    # Test execution permissions
    EXECUTE_TESTS = "execute:tests"
    VIEW_TEST_RESULTS = "view:test_results"
    MANAGE_TEST_ENVIRONMENTS = "manage:test_environments"
    
    # User management permissions
    MANAGE_USERS = "manage:users"
    VIEW_USER_LIST = "view:user_list"
    INVITE_USERS = "invite:users"
    REMOVE_USERS = "remove:users"
    UPDATE_USER_ROLES = "update:user_roles"
    
    # Organization permissions
    MANAGE_ORGANIZATION_SETTINGS = "manage:organization_settings"
    VIEW_ORGANIZATION_ANALYTICS = "view:organization_analytics"
    MANAGE_ORGANIZATION = "manage:organization"
    
    # Analytics and reporting
    READ_ANALYTICS = "read:analytics"
    EXPORT_DATA = "export:data"
    
    # System administration
    SYSTEM_ADMIN = "system:admin"
    MANAGE_ALL_ORGANIZATIONS = "manage:all_organizations"
    
    # Wildcard permissions
    ALL_ORGANIZATION = "*:organization"  # All permissions within organization
    ALL_SYSTEM = "*:*"  # All system permissions


class RBACService:
    """Role-Based Access Control service for permission management."""
    
    def __init__(self):
        """Initialize RBAC service with permission mappings."""
        self._role_permissions = self._initialize_role_permissions()
        self._permission_hierarchy = self._initialize_permission_hierarchy()
        logger.info("RBAC service initialized")
    
    def _initialize_role_permissions(self) -> Dict[Role, Set[str]]:
        """Initialize role-to-permissions mapping."""
        return {
            Role.USER: {
                Permission.READ_PROJECTS.value,
                Permission.CREATE_PROJECTS.value,
                Permission.UPDATE_OWN_PROJECTS.value,
                Permission.DELETE_OWN_PROJECTS.value,
                Permission.READ_TEST_CASES.value,
                Permission.CREATE_TEST_CASES.value,
                Permission.UPDATE_OWN_TEST_CASES.value,
                Permission.DELETE_OWN_TEST_CASES.value,
                Permission.EXECUTE_TESTS.value,
                Permission.VIEW_TEST_RESULTS.value,
            },
            Role.ORG_ADMIN: {
                # All USER permissions plus:
                Permission.READ_PROJECTS.value,
                Permission.CREATE_PROJECTS.value,
                Permission.UPDATE_OWN_PROJECTS.value,
                Permission.UPDATE_ALL_PROJECTS.value,
                Permission.DELETE_OWN_PROJECTS.value,
                Permission.DELETE_ALL_PROJECTS.value,
                Permission.READ_TEST_CASES.value,
                Permission.CREATE_TEST_CASES.value,
                Permission.UPDATE_OWN_TEST_CASES.value,
                Permission.UPDATE_ALL_TEST_CASES.value,
                Permission.DELETE_OWN_TEST_CASES.value,
                Permission.DELETE_ALL_TEST_CASES.value,
                Permission.EXECUTE_TESTS.value,
                Permission.VIEW_TEST_RESULTS.value,
                Permission.MANAGE_TEST_ENVIRONMENTS.value,
                Permission.MANAGE_USERS.value,
                Permission.VIEW_USER_LIST.value,
                Permission.INVITE_USERS.value,
                Permission.REMOVE_USERS.value,
                Permission.UPDATE_USER_ROLES.value,
                Permission.MANAGE_ORGANIZATION_SETTINGS.value,
                Permission.VIEW_ORGANIZATION_ANALYTICS.value,
                Permission.READ_ANALYTICS.value,
                Permission.EXPORT_DATA.value,
                Permission.ALL_ORGANIZATION.value,
            },
            Role.ADMIN: {
                # All permissions (system-wide)
                Permission.ALL_SYSTEM.value,
            }
        }
    
    def _initialize_permission_hierarchy(self) -> Dict[str, List[str]]:
        """Initialize permission hierarchy for inheritance."""
        return {
            Permission.ALL_SYSTEM.value: [perm.value for perm in Permission],
            Permission.ALL_ORGANIZATION.value: [
                Permission.READ_PROJECTS.value,
                Permission.CREATE_PROJECTS.value,
                Permission.UPDATE_ALL_PROJECTS.value,
                Permission.DELETE_ALL_PROJECTS.value,
                Permission.READ_TEST_CASES.value,
                Permission.CREATE_TEST_CASES.value,
                Permission.UPDATE_ALL_TEST_CASES.value,
                Permission.DELETE_ALL_TEST_CASES.value,
                Permission.EXECUTE_TESTS.value,
                Permission.VIEW_TEST_RESULTS.value,
                Permission.MANAGE_TEST_ENVIRONMENTS.value,
                Permission.MANAGE_USERS.value,
                Permission.VIEW_USER_LIST.value,
                Permission.INVITE_USERS.value,
                Permission.REMOVE_USERS.value,
                Permission.UPDATE_USER_ROLES.value,
                Permission.MANAGE_ORGANIZATION_SETTINGS.value,
                Permission.VIEW_ORGANIZATION_ANALYTICS.value,
                Permission.READ_ANALYTICS.value,
                Permission.EXPORT_DATA.value,
            ]
        }
    
    def get_role_permissions(self, role: Role) -> Set[str]:
        """
        Get all permissions for a role.
        
        Args:
            role: User role
            
        Returns:
            Set of permission strings
        """
        base_permissions = self._role_permissions.get(role, set())
        expanded_permissions = set()
        
        for permission in base_permissions:
            expanded_permissions.add(permission)
            # Add inherited permissions
            if permission in self._permission_hierarchy:
                expanded_permissions.update(self._permission_hierarchy[permission])
        
        return expanded_permissions
    
    async def check_permission(
        self,
        user_id: str,
        organization_id: str,
        required_permission: str,
        resource_owner_id: Optional[str] = None
    ) -> bool:
        """
        Check if user has required permission in organization.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            required_permission: Permission to check
            resource_owner_id: Owner of the resource (for "own" permissions)
            
        Returns:
            True if user has permission, False otherwise
        """
        try:
            # Get user's role in organization
            user_org = await UserOrganization.find_one({
                "user_id": user_id,
                "organization_id": organization_id,
                "is_active": True
            })
            
            if not user_org:
                logger.debug(f"User {user_id} not found in organization {organization_id}")
                return False
            
            role = Role(user_org.role)
            user_permissions = self.get_role_permissions(role)
            
            # Check for exact permission match
            if required_permission in user_permissions:
                return True
            
            # Check for wildcard permissions
            if Permission.ALL_SYSTEM.value in user_permissions:
                return True
            
            if Permission.ALL_ORGANIZATION.value in user_permissions:
                return True
            
            # Handle "own" permissions
            if required_permission.endswith(":own_projects") or required_permission.endswith(":own_test_cases"):
                if resource_owner_id and resource_owner_id == user_id:
                    # User can access their own resources
                    base_permission = required_permission.replace(":own_", ":")
                    return base_permission in user_permissions
            
            # Check for pattern matching (e.g., "read:*", "*:projects")
            for permission in user_permissions:
                if self._permission_matches_pattern(required_permission, permission):
                    return True
            
            logger.debug(f"Permission denied: {user_id} lacks {required_permission} in {organization_id}")
            return False
            
        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False
    
    def _permission_matches_pattern(self, required: str, granted: str) -> bool:
        """
        Check if a required permission matches a granted permission pattern.
        
        Args:
            required: Required permission string
            granted: Granted permission string (may contain wildcards)
            
        Returns:
            True if permission matches pattern
        """
        if granted == "*:*":
            return True
        
        if ":" not in required or ":" not in granted:
            return required == granted
        
        req_action, req_resource = required.split(":", 1)
        grant_action, grant_resource = granted.split(":", 1)
        
        # Check action wildcard
        if grant_action == "*" and (grant_resource == req_resource or grant_resource == "*"):
            return True
        
        # Check resource wildcard
        if grant_resource == "*" and grant_action == req_action:
            return True
        
        return False
    
    async def get_user_roles_in_organization(
        self,
        user_id: str,
        organization_id: str
    ) -> Optional[Role]:
        """
        Get user's role in a specific organization.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            
        Returns:
            User's role or None if not in organization
        """
        user_org = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id,
            "is_active": True
        })
        
        if user_org:
            return Role(user_org.role)
        
        return None
    
    async def get_user_permissions_in_organization(
        self,
        user_id: str,
        organization_id: str
    ) -> Set[str]:
        """
        Get all permissions for user in organization.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            
        Returns:
            Set of permission strings
        """
        role = await self.get_user_roles_in_organization(user_id, organization_id)
        if role:
            return self.get_role_permissions(role)
        return set()
    
    async def get_users_with_permission(
        self,
        organization_id: str,
        required_permission: str
    ) -> List[Dict[str, Any]]:
        """
        Get all users in organization with specific permission.
        
        Args:
            organization_id: Organization identifier
            required_permission: Permission to check
            
        Returns:
            List of user dictionaries with permission info
        """
        # Get all users in organization
        user_orgs = await UserOrganization.find({
            "organization_id": organization_id,
            "is_active": True
        }).to_list()
        
        users_with_permission = []
        
        for user_org in user_orgs:
            role = Role(user_org.role)
            permissions = self.get_role_permissions(role)
            
            # Check if user has the required permission
            has_permission = (
                required_permission in permissions or
                Permission.ALL_SYSTEM.value in permissions or
                Permission.ALL_ORGANIZATION.value in permissions or
                any(self._permission_matches_pattern(required_permission, p) for p in permissions)
            )
            
            if has_permission:
                # Get user details
                user = await User.find_one({"user_id": user_org.user_id})
                if user:
                    user_dict = user.to_profile_dict()
                    user_dict["role"] = user_org.role
                    user_dict["permissions"] = list(permissions)
                    users_with_permission.append(user_dict)
        
        return users_with_permission
    
    def validate_role_transition(
        self,
        current_role: Role,
        new_role: Role,
        requester_role: Role
    ) -> bool:
        """
        Validate if a role transition is allowed.
        
        Args:
            current_role: Current user role
            new_role: Desired new role
            requester_role: Role of user making the request
            
        Returns:
            True if transition is allowed, False otherwise
        """
        # ADMIN can change any role
        if requester_role == Role.ADMIN:
            return True
        
        # ORG_ADMIN can manage USER roles but not other ORG_ADMINs or ADMINs
        if requester_role == Role.ORG_ADMIN:
            if current_role == Role.USER and new_role in [Role.USER, Role.ORG_ADMIN]:
                return True
            if current_role == Role.ORG_ADMIN and new_role == Role.USER:
                return True
        
        # Users cannot change roles
        return False
    
    async def audit_permission_check(
        self,
        user_id: str,
        organization_id: str,
        permission: str,
        resource_id: Optional[str] = None,
        granted: bool = False
    ):
        """
        Audit permission check for compliance and security monitoring.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            permission: Permission that was checked
            resource_id: Resource identifier (optional)
            granted: Whether permission was granted
        """
        # This would typically log to an audit system
        logger.info(
            f"AUDIT: user={user_id} org={organization_id} permission={permission} "
            f"resource={resource_id} granted={granted}"
        )
