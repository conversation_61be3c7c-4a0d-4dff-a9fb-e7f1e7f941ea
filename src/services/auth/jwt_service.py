"""
JWT Service for QAK Authentication

Handles JWT token creation, validation, and management for multi-tenant authentication.
Uses HS256 algorithm with configurable expiration times.
"""

import os
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
from jose import JWTError, jwt
from pydantic import BaseModel, Field, field_validator
import logging

logger = logging.getLogger(__name__)


class TokenType(str, Enum):
    """JWT token types."""
    ACCESS = "access"
    REFRESH = "refresh"


class TokenPayload(BaseModel):
    """JWT token payload model with validation."""
    
    # Subject (user identifier)
    sub: str = Field(..., description="User ID")
    
    # Token type
    token_type: TokenType = Field(..., description="Type of token (access/refresh)")
    
    # Organization context
    organization_id: Optional[str] = Field(None, description="Organization ID for multi-tenancy")
    role: Optional[str] = Field(None, description="User role within organization")
    
    # Standard JWT claims
    exp: int = Field(..., description="Expiration timestamp")
    iat: int = Field(..., description="Issued at timestamp")
    jti: Optional[str] = Field(None, description="JWT ID for token tracking")
    
    # Additional claims
    email: Optional[str] = Field(None, description="User email")
    
    @field_validator('sub')
    @classmethod
    def validate_sub(cls, v):
        """Validate subject field."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Subject (user ID) cannot be empty")
        return v.strip()
    
    @field_validator('token_type', mode='before')
    @classmethod
    def validate_token_type(cls, v):
        """Validate and convert token type."""
        if isinstance(v, str):
            return TokenType(v)
        return v


class JWTService:
    """JWT token management service."""
    
    def __init__(self):
        """Initialize JWT service with configuration from environment."""
        self.secret_key = os.getenv("JWT_SECRET_KEY")
        if not self.secret_key:
            raise ValueError("JWT_SECRET_KEY environment variable is required")
        
        self.algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        self.access_token_expire_minutes = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "15"))
        self.refresh_token_expire_days = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))
        
        logger.info(f"JWT Service initialized with {self.algorithm} algorithm")
        logger.info(f"Access token expiry: {self.access_token_expire_minutes} minutes")
        logger.info(f"Refresh token expiry: {self.refresh_token_expire_days} days")
    
    def create_access_token(
        self, 
        user_id: str, 
        organization_id: str, 
        role: str,
        email: Optional[str] = None,
        extra_claims: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create an access token for user authentication.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier for multi-tenancy
            role: User role within the organization
            email: User email (optional)
            extra_claims: Additional claims to include (optional)
            
        Returns:
            Encoded JWT access token
        """
        now = datetime.utcnow()
        expire = now + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "sub": user_id,
            "token_type": TokenType.ACCESS.value,
            "organization_id": organization_id,
            "role": role,
            "exp": int(expire.timestamp()),
            "iat": int(now.timestamp()),
        }
        
        if email:
            payload["email"] = email
            
        if extra_claims:
            payload.update(extra_claims)
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            logger.debug(f"Created access token for user {user_id} in org {organization_id}")
            return token
        except Exception as e:
            logger.error(f"Failed to create access token: {e}")
            raise
    
    def create_refresh_token(
        self, 
        user_id: str,
        jti: Optional[str] = None
    ) -> str:
        """
        Create a refresh token for token renewal.
        
        Args:
            user_id: User identifier
            jti: JWT ID for token tracking (optional)
            
        Returns:
            Encoded JWT refresh token
        """
        now = datetime.utcnow()
        expire = now + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "sub": user_id,
            "token_type": TokenType.REFRESH.value,
            "exp": int(expire.timestamp()),
            "iat": int(now.timestamp()),
        }
        
        if jti:
            payload["jti"] = jti
        
        try:
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            logger.debug(f"Created refresh token for user {user_id}")
            return token
        except Exception as e:
            logger.error(f"Failed to create refresh token: {e}")
            raise
    
    def validate_token(self, token: str) -> TokenPayload:
        """
        Validate and decode a JWT token.
        
        Args:
            token: JWT token to validate
            
        Returns:
            Decoded token payload
            
        Raises:
            JWTError: If token is invalid or expired
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Validate required fields
            if "sub" not in payload:
                raise JWTError("Token missing subject")
            if "token_type" not in payload:
                raise JWTError("Token missing type")
            if "exp" not in payload:
                raise JWTError("Token missing expiration")
            if "iat" not in payload:
                raise JWTError("Token missing issued at")
            
            # Create and validate payload model
            token_payload = TokenPayload(**payload)
            
            logger.debug(f"Successfully validated {token_payload.token_type} token for user {token_payload.sub}")
            return token_payload
            
        except JWTError as e:
            logger.warning(f"JWT validation failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error validating token: {e}")
            raise JWTError(f"Token validation error: {e}")
    
    def decode_token_without_verification(self, token: str) -> Dict[str, Any]:
        """
        Decode token without verification (for debugging/inspection).
        
        Args:
            token: JWT token to decode
            
        Returns:
            Decoded token payload
        """
        try:
            return jwt.get_unverified_claims(token)
        except Exception as e:
            logger.error(f"Failed to decode token: {e}")
            raise JWTError(f"Token decode error: {e}")
    
    def is_token_expired(self, token: str) -> bool:
        """
        Check if a token is expired without full validation.
        
        Args:
            token: JWT token to check
            
        Returns:
            True if token is expired, False otherwise
        """
        try:
            payload = self.decode_token_without_verification(token)
            exp = payload.get("exp")
            if not exp:
                return True
            
            return datetime.utcnow().timestamp() > exp
        except Exception:
            return True
    
    def get_token_expiry(self, token: str) -> Optional[datetime]:
        """
        Get the expiration time of a token.
        
        Args:
            token: JWT token
            
        Returns:
            Expiration datetime or None if invalid
        """
        try:
            payload = self.decode_token_without_verification(token)
            exp = payload.get("exp")
            if exp:
                return datetime.fromtimestamp(exp)
            return None
        except Exception:
            return None
