"""
Password Service for QAK Authentication

Handles password hashing, verification, and strength validation using bcrypt.
Implements configurable password policies for security compliance.
"""

import os
import re
from typing import List, Dict, Any
from passlib.context import CryptContext
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)


class PasswordPolicy(BaseModel):
    """Password policy configuration model."""
    
    min_length: int = 8
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_digits: bool = True
    require_special_chars: bool = True
    special_chars: str = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    max_length: int = 128
    
    @classmethod
    def from_env(cls) -> "PasswordPolicy":
        """Create password policy from environment variables."""
        return cls(
            min_length=int(os.getenv("PASSWORD_MIN_LENGTH", "8")),
            require_special_chars=os.getenv("PASSWORD_REQUIRE_SPECIAL_CHARS", "true").lower() == "true",
            max_length=int(os.getenv("PASSWORD_MAX_LENGTH", "128")),
        )


class PasswordValidationResult(BaseModel):
    """Result of password validation."""
    
    is_valid: bool
    errors: List[str] = []
    score: int = 0  # Password strength score (0-100)
    
    @property
    def is_strong(self) -> bool:
        """Check if password is considered strong (score >= 70)."""
        return self.score >= 70


class PasswordService:
    """Password management service with bcrypt hashing and validation."""
    
    def __init__(self, policy: PasswordPolicy = None):
        """
        Initialize password service.
        
        Args:
            policy: Password policy configuration (defaults to environment-based policy)
        """
        # Configure bcrypt context with secure defaults
        bcrypt_rounds = int(os.getenv("PASSWORD_BCRYPT_ROUNDS", "12"))
        self.pwd_context = CryptContext(
            schemes=["bcrypt"],
            deprecated="auto",
            bcrypt__rounds=bcrypt_rounds
        )
        
        # Set password policy
        self.policy = policy or PasswordPolicy.from_env()
        
        logger.info(f"Password service initialized with bcrypt rounds: {bcrypt_rounds}")
        logger.info(f"Password policy: min_length={self.policy.min_length}, "
                   f"require_special_chars={self.policy.require_special_chars}")
    
    def hash_password(self, password: str) -> str:
        """
        Hash a password using bcrypt.
        
        Args:
            password: Plain text password to hash
            
        Returns:
            Bcrypt hashed password
            
        Raises:
            ValueError: If password is invalid
        """
        if not password:
            raise ValueError("Password cannot be empty")
        
        # Validate password before hashing
        validation_result = self.validate_password(password)
        if not validation_result.is_valid:
            raise ValueError(f"Password validation failed: {', '.join(validation_result.errors)}")
        
        try:
            hashed = self.pwd_context.hash(password)
            logger.debug("Password hashed successfully")
            return hashed
        except Exception as e:
            logger.error(f"Failed to hash password: {e}")
            raise ValueError(f"Password hashing failed: {e}")
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against its hash.
        
        Args:
            plain_password: Plain text password to verify
            hashed_password: Bcrypt hashed password to verify against
            
        Returns:
            True if password matches, False otherwise
        """
        if not plain_password or not hashed_password:
            return False
        
        try:
            is_valid = self.pwd_context.verify(plain_password, hashed_password)
            logger.debug(f"Password verification: {'success' if is_valid else 'failed'}")
            return is_valid
        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False
    
    def needs_rehash(self, hashed_password: str) -> bool:
        """
        Check if a password hash needs to be updated.
        
        Args:
            hashed_password: Bcrypt hashed password to check
            
        Returns:
            True if hash needs updating, False otherwise
        """
        try:
            return self.pwd_context.needs_update(hashed_password)
        except Exception as e:
            logger.error(f"Error checking hash update need: {e}")
            return False
    
    def validate_password(self, password: str) -> PasswordValidationResult:
        """
        Validate password against policy requirements.
        
        Args:
            password: Password to validate
            
        Returns:
            PasswordValidationResult with validation details
        """
        errors = []
        score = 0
        
        if not password:
            return PasswordValidationResult(is_valid=False, errors=["Password cannot be empty"])
        
        # Length validation
        if len(password) < self.policy.min_length:
            errors.append(f"Password must be at least {self.policy.min_length} characters long")
        else:
            score += 20
        
        if len(password) > self.policy.max_length:
            errors.append(f"Password cannot exceed {self.policy.max_length} characters")
        
        # Character type requirements
        if self.policy.require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        elif re.search(r'[A-Z]', password):
            score += 15
        
        if self.policy.require_lowercase and not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        elif re.search(r'[a-z]', password):
            score += 15
        
        if self.policy.require_digits and not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")
        elif re.search(r'\d', password):
            score += 15
        
        if self.policy.require_special_chars:
            special_pattern = f"[{re.escape(self.policy.special_chars)}]"
            if not re.search(special_pattern, password):
                errors.append(f"Password must contain at least one special character: {self.policy.special_chars}")
            else:
                score += 15
        elif re.search(f"[{re.escape(self.policy.special_chars)}]", password):
            score += 10
        
        # Additional strength scoring
        if len(password) >= 12:
            score += 10
        if len(password) >= 16:
            score += 10
        
        # Check for common patterns (reduce score)
        if re.search(r'(.)\1{2,}', password):  # Repeated characters
            score -= 10
        if re.search(r'(012|123|234|345|456|567|678|789|890)', password):  # Sequential numbers
            score -= 10
        if re.search(r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)', password.lower()):  # Sequential letters
            score -= 10
        
        # Ensure score is within bounds
        score = max(0, min(100, score))
        
        is_valid = len(errors) == 0
        
        return PasswordValidationResult(
            is_valid=is_valid,
            errors=errors,
            score=score
        )
    
    def generate_password_requirements(self) -> Dict[str, Any]:
        """
        Get password requirements for frontend display.
        
        Returns:
            Dictionary with password requirements
        """
        return {
            "min_length": self.policy.min_length,
            "max_length": self.policy.max_length,
            "require_uppercase": self.policy.require_uppercase,
            "require_lowercase": self.policy.require_lowercase,
            "require_digits": self.policy.require_digits,
            "require_special_chars": self.policy.require_special_chars,
            "special_chars": self.policy.special_chars,
        }
    
    def get_password_strength_feedback(self, password: str) -> Dict[str, Any]:
        """
        Get detailed password strength feedback.
        
        Args:
            password: Password to analyze
            
        Returns:
            Dictionary with strength analysis
        """
        validation_result = self.validate_password(password)
        
        feedback = {
            "score": validation_result.score,
            "is_valid": validation_result.is_valid,
            "is_strong": validation_result.is_strong,
            "errors": validation_result.errors,
            "suggestions": []
        }
        
        # Add improvement suggestions
        if validation_result.score < 70:
            if len(password) < 12:
                feedback["suggestions"].append("Consider using a longer password (12+ characters)")
            if not re.search(r'[A-Z]', password):
                feedback["suggestions"].append("Add uppercase letters")
            if not re.search(r'[a-z]', password):
                feedback["suggestions"].append("Add lowercase letters")
            if not re.search(r'\d', password):
                feedback["suggestions"].append("Add numbers")
            if not re.search(f"[{re.escape(self.policy.special_chars)}]", password):
                feedback["suggestions"].append("Add special characters")
        
        return feedback
