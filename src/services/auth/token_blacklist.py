"""
Token Blacklist Service for QAK Authentication

Manages JWT token blacklisting using MongoDB TTL collections.
Provides secure token invalidation for logout and security purposes.
"""

import os
from typing import Optional
from datetime import datetime, timedelta
from beanie import Document, Indexed
from pydantic import Field, ConfigDict
from motor.motor_asyncio import AsyncIOMotorClient
import logging

logger = logging.getLogger(__name__)


class BlacklistedToken(Document):
    """MongoDB document for blacklisted JWT tokens."""
    
    # Token identification
    token_jti: Indexed(str, unique=True) = Field(..., description="JWT ID (jti claim)")
    token_hash: Indexed(str) = Field(..., description="SHA256 hash of the token")
    
    # Token metadata
    user_id: Indexed(str) = Field(..., description="User ID who owned the token")
    token_type: str = Field(..., description="Type of token (access/refresh)")
    
    # Blacklist metadata
    blacklisted_at: Indexed(datetime) = Field(default_factory=datetime.utcnow)
    reason: str = Field(default="logout", description="Reason for blacklisting")
    
    # TTL expiration - MongoDB will automatically delete expired documents
    expires_at: Indexed(datetime) = Field(..., description="When this blacklist entry expires")
    
    # MongoDB document settings
    class Settings:
        name = "blacklisted_tokens"
        indexes = [
            "token_jti",
            "token_hash", 
            "user_id",
            "blacklisted_at",
            # TTL index for automatic cleanup
            ("expires_at", 1),
        ]
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )


class TokenBlacklistService:
    """Service for managing JWT token blacklisting."""
    
    def __init__(self):
        """Initialize the token blacklist service."""
        self.access_token_expire_minutes = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "15"))
        self.refresh_token_expire_days = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))
        
        logger.info("Token blacklist service initialized")
    
    async def add_to_blacklist(
        self,
        token_jti: str,
        token_hash: str,
        user_id: str,
        token_type: str,
        expires_at: datetime,
        reason: str = "logout"
    ) -> bool:
        """
        Add a token to the blacklist.
        
        Args:
            token_jti: JWT ID from token claims
            token_hash: SHA256 hash of the token
            user_id: User ID who owns the token
            token_type: Type of token (access/refresh)
            expires_at: When the token expires
            reason: Reason for blacklisting
            
        Returns:
            True if successfully blacklisted, False otherwise
        """
        try:
            blacklisted_token = BlacklistedToken(
                token_jti=token_jti,
                token_hash=token_hash,
                user_id=user_id,
                token_type=token_type,
                expires_at=expires_at,
                reason=reason
            )
            
            await blacklisted_token.insert()
            
            logger.info(f"Token blacklisted: jti={token_jti}, user={user_id}, reason={reason}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to blacklist token: {e}")
            return False
    
    async def is_blacklisted(self, token_jti: str = None, token_hash: str = None) -> bool:
        """
        Check if a token is blacklisted.
        
        Args:
            token_jti: JWT ID to check (optional)
            token_hash: Token hash to check (optional)
            
        Returns:
            True if token is blacklisted, False otherwise
        """
        if not token_jti and not token_hash:
            return False
        
        try:
            query = {}
            if token_jti:
                query["token_jti"] = token_jti
            if token_hash:
                query["token_hash"] = token_hash
            
            blacklisted_token = await BlacklistedToken.find_one(query)
            
            is_blacklisted = blacklisted_token is not None
            
            if is_blacklisted:
                logger.debug(f"Token is blacklisted: jti={token_jti}")
            
            return is_blacklisted
            
        except Exception as e:
            logger.error(f"Error checking blacklist: {e}")
            # Fail secure - assume blacklisted if we can't check
            return True
    
    async def blacklist_user_tokens(
        self,
        user_id: str,
        token_type: Optional[str] = None,
        reason: str = "security_logout"
    ) -> int:
        """
        Blacklist all tokens for a specific user.
        
        Args:
            user_id: User ID whose tokens to blacklist
            token_type: Specific token type to blacklist (optional)
            reason: Reason for blacklisting
            
        Returns:
            Number of tokens blacklisted
        """
        try:
            # This is a simplified implementation
            # In a real system, you'd need to track active tokens
            # For now, we'll create blacklist entries with future expiration
            
            now = datetime.utcnow()
            access_expires = now + timedelta(minutes=self.access_token_expire_minutes)
            refresh_expires = now + timedelta(days=self.refresh_token_expire_days)
            
            count = 0
            
            # Blacklist access tokens if not specified or specifically requested
            if not token_type or token_type == "access":
                access_blacklist = BlacklistedToken(
                    token_jti=f"user_logout_access_{user_id}_{int(now.timestamp())}",
                    token_hash=f"user_logout_access_hash_{user_id}_{int(now.timestamp())}",
                    user_id=user_id,
                    token_type="access",
                    expires_at=access_expires,
                    reason=reason
                )
                await access_blacklist.insert()
                count += 1
            
            # Blacklist refresh tokens if not specified or specifically requested
            if not token_type or token_type == "refresh":
                refresh_blacklist = BlacklistedToken(
                    token_jti=f"user_logout_refresh_{user_id}_{int(now.timestamp())}",
                    token_hash=f"user_logout_refresh_hash_{user_id}_{int(now.timestamp())}",
                    user_id=user_id,
                    token_type="refresh",
                    expires_at=refresh_expires,
                    reason=reason
                )
                await refresh_blacklist.insert()
                count += 1
            
            logger.info(f"Blacklisted {count} tokens for user {user_id}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to blacklist user tokens: {e}")
            return 0
    
    async def cleanup_expired_tokens(self) -> int:
        """
        Manually cleanup expired blacklist entries.
        Note: MongoDB TTL index should handle this automatically.
        
        Returns:
            Number of entries cleaned up
        """
        try:
            now = datetime.utcnow()
            
            # Find and delete expired entries
            expired_tokens = await BlacklistedToken.find(
                BlacklistedToken.expires_at < now
            ).to_list()
            
            count = 0
            for token in expired_tokens:
                await token.delete()
                count += 1
            
            if count > 0:
                logger.info(f"Cleaned up {count} expired blacklist entries")
            
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired tokens: {e}")
            return 0
    
    async def get_blacklist_stats(self) -> dict:
        """
        Get statistics about the token blacklist.
        
        Returns:
            Dictionary with blacklist statistics
        """
        try:
            total_count = await BlacklistedToken.count()
            
            # Count by token type
            access_count = await BlacklistedToken.find(
                BlacklistedToken.token_type == "access"
            ).count()
            
            refresh_count = await BlacklistedToken.find(
                BlacklistedToken.token_type == "refresh"
            ).count()
            
            # Count expired entries
            now = datetime.utcnow()
            expired_count = await BlacklistedToken.find(
                BlacklistedToken.expires_at < now
            ).count()
            
            return {
                "total_blacklisted": total_count,
                "access_tokens": access_count,
                "refresh_tokens": refresh_count,
                "expired_entries": expired_count,
                "active_entries": total_count - expired_count,
            }
            
        except Exception as e:
            logger.error(f"Failed to get blacklist stats: {e}")
            return {
                "total_blacklisted": 0,
                "access_tokens": 0,
                "refresh_tokens": 0,
                "expired_entries": 0,
                "active_entries": 0,
            }
    
    def calculate_token_expiry(self, token_type: str) -> datetime:
        """
        Calculate when a token expires based on its type.
        
        Args:
            token_type: Type of token (access/refresh)
            
        Returns:
            Expiration datetime
        """
        now = datetime.utcnow()
        
        if token_type == "access":
            return now + timedelta(minutes=self.access_token_expire_minutes)
        elif token_type == "refresh":
            return now + timedelta(days=self.refresh_token_expire_days)
        else:
            # Default to access token expiry for unknown types
            return now + timedelta(minutes=self.access_token_expire_minutes)
