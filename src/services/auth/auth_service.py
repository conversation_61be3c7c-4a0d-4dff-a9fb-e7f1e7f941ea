"""
Core Authentication Service for QAK

Orchestrates user registration, login, token management, and organization creation.
Integrates all authentication components for multi-tenant architecture.
"""

import os
import hashlib
from typing import Optional, Tuple, Dict, Any
from datetime import datetime, timedelta
from jose import JWTError
from src.database.models.user import User
from src.database.models.organization import Organization
from src.database.models.user_organization import UserOrganization, Role
from src.services.auth.jwt_service import JWTService, TokenPayload, TokenType
from src.services.auth.password_service import PasswordService
from src.services.auth.email_service import EmailService
from src.services.auth.token_blacklist import TokenBlacklistService, BlacklistedToken
from src.models.auth_models import (
    UserRegistrationRequest, LoginRequest, LoginResponse, 
    UserProfileResponse, OrganizationResponse, TokenResponse,
    RefreshTokenResponse, UserMeResponse
)
from src.exceptions.auth_exceptions import (
    InvalidCredentialsException, UserAlreadyExistsException,
    InvalidTokenException, TokenExpiredException, UserNotFoundException,
    OrganizationNotFoundException, AccountDeactivatedException,
    EmailNotVerifiedException, InvalidRefreshTokenException,
    TokenBlacklistedException
)
import logging

logger = logging.getLogger(__name__)


class AuthService:
    """Core authentication service orchestrating all auth operations."""
    
    def __init__(self):
        """Initialize authentication service with all required components."""
        self.jwt_service = JWTService()
        self.password_service = PasswordService()
        self.email_service = EmailService()
        self.blacklist_service = TokenBlacklistService()
        
        # Configuration
        self.access_token_expire_minutes = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "15"))
        self.refresh_token_expire_days = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))
        
        logger.info("Authentication service initialized")
    
    async def register_user(self, registration_data: UserRegistrationRequest) -> LoginResponse:
        """
        Register a new user and create their organization.
        
        Args:
            registration_data: User registration information
            
        Returns:
            LoginResponse with tokens and user/organization info
            
        Raises:
            UserAlreadyExistsException: If email is already registered
            InvalidPasswordException: If password doesn't meet policy
        """
        logger.info(f"Starting user registration for email: {registration_data.email}")
        
        # Validate and check email uniqueness
        email_validation = await self.email_service.validate_and_check_uniqueness(
            registration_data.email
        )
        
        if not email_validation.is_valid:
            raise UserAlreadyExistsException(registration_data.email)
        
        # Hash password
        password_hash = self.password_service.hash_password(registration_data.password)
        
        # Create organization first
        organization = Organization.create_with_auto_slug(
            name=registration_data.organization_name,
            description=f"Organization for {registration_data.organization_name}"
        )
        await organization.insert()
        
        # Create user
        user = User(
            email=email_validation.normalized_email,
            password_hash=password_hash,
            first_name=registration_data.first_name,
            last_name=registration_data.last_name,
            is_active=True,
            email_verified=False  # Will be verified later
        )
        await user.insert()
        
        # Create user-organization relationship with ORG_ADMIN role
        user_org = UserOrganization(
            user_id=user.user_id,
            organization_id=organization.organization_id,
            role=Role.ORG_ADMIN.value,
            is_active=True
        )
        await user_org.insert()
        
        logger.info(f"User registered successfully: {user.user_id} in org {organization.organization_id}")
        
        # Generate tokens and return login response
        return await self._create_login_response(user, organization, user_org)
    
    async def authenticate_user(self, login_data: LoginRequest) -> LoginResponse:
        """
        Authenticate user with email and password.
        
        Args:
            login_data: Login credentials
            
        Returns:
            LoginResponse with tokens and user/organization info
            
        Raises:
            InvalidCredentialsException: If credentials are invalid
            AccountDeactivatedException: If account is deactivated
        """
        logger.info(f"Authentication attempt for email: {login_data.email}")
        
        # Find user by email
        user = await User.find_one({"email": login_data.email.lower()})
        if not user:
            logger.warning(f"Authentication failed: user not found for {login_data.email}")
            raise InvalidCredentialsException()
        
        # Verify password
        if not self.password_service.verify_password(login_data.password, user.password_hash):
            logger.warning(f"Authentication failed: invalid password for {login_data.email}")
            raise InvalidCredentialsException()
        
        # Check if account is active
        if not user.is_active:
            logger.warning(f"Authentication failed: account deactivated for {login_data.email}")
            raise AccountDeactivatedException()
        
        # Get user's primary organization (for now, get the first active one)
        user_org = await UserOrganization.find_one({
            "user_id": user.user_id,
            "is_active": True
        })
        
        if not user_org:
            logger.error(f"No active organization found for user {user.user_id}")
            raise InvalidCredentialsException("No active organization found")
        
        # Get organization details
        organization = await Organization.find_one({
            "organization_id": user_org.organization_id
        })
        
        if not organization:
            logger.error(f"Organization not found: {user_org.organization_id}")
            raise OrganizationNotFoundException(user_org.organization_id)
        
        # Update last login
        user.update_last_login()
        await user.save()
        
        # Update last accessed for organization relationship
        user_org.update_last_accessed()
        await user_org.save()
        
        logger.info(f"User authenticated successfully: {user.user_id}")
        
        return await self._create_login_response(user, organization, user_org)
    
    async def refresh_tokens(self, refresh_token: str) -> RefreshTokenResponse:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            RefreshTokenResponse with new tokens
            
        Raises:
            InvalidRefreshTokenException: If refresh token is invalid
            TokenBlacklistedException: If token is blacklisted
        """
        try:
            # Validate refresh token
            token_payload = self.jwt_service.validate_token(refresh_token)
            
            if token_payload.token_type != TokenType.REFRESH:
                raise InvalidRefreshTokenException("Invalid token type")
            
            # Check if token is blacklisted
            token_hash = self._hash_token(refresh_token)
            if await self.blacklist_service.is_blacklisted(
                token_jti=token_payload.jti,
                token_hash=token_hash
            ):
                raise TokenBlacklistedException()
            
            # Get user and organization info
            user = await User.find_one({"user_id": token_payload.sub})
            if not user or not user.is_active:
                raise InvalidRefreshTokenException("User not found or inactive")
            
            # Get user's organization relationship
            user_org = await UserOrganization.find_one({
                "user_id": user.user_id,
                "is_active": True
            })
            
            if not user_org:
                raise InvalidRefreshTokenException("No active organization found")
            
            # Blacklist old refresh token
            expires_at = self.blacklist_service.calculate_token_expiry("refresh")
            await self.blacklist_service.add_to_blacklist(
                token_jti=token_payload.jti or f"refresh_{user.user_id}_{int(datetime.utcnow().timestamp())}",
                token_hash=token_hash,
                user_id=user.user_id,
                token_type="refresh",
                expires_at=expires_at,
                reason="token_refresh"
            )
            
            # Generate new tokens
            new_access_token = self.jwt_service.create_access_token(
                user_id=user.user_id,
                organization_id=user_org.organization_id,
                role=user_org.role,
                email=user.email
            )
            
            new_refresh_token = self.jwt_service.create_refresh_token(user_id=user.user_id)
            
            logger.info(f"Tokens refreshed for user: {user.user_id}")
            
            return RefreshTokenResponse(
                access_token=new_access_token,
                refresh_token=new_refresh_token,
                expires_in=self.access_token_expire_minutes * 60
            )
            
        except JWTError as e:
            logger.warning(f"Invalid refresh token: {e}")
            raise InvalidRefreshTokenException(str(e))
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            raise InvalidRefreshTokenException("Token refresh failed")
    
    async def logout_user(self, access_token: str, refresh_token: Optional[str] = None) -> bool:
        """
        Logout user by blacklisting tokens.
        
        Args:
            access_token: User's access token
            refresh_token: User's refresh token (optional)
            
        Returns:
            True if logout successful
        """
        try:
            # Validate access token to get user info
            token_payload = self.jwt_service.validate_token(access_token)
            
            # Blacklist access token
            access_expires = self.blacklist_service.calculate_token_expiry("access")
            await self.blacklist_service.add_to_blacklist(
                token_jti=token_payload.jti or f"access_{token_payload.sub}_{int(datetime.utcnow().timestamp())}",
                token_hash=self._hash_token(access_token),
                user_id=token_payload.sub,
                token_type="access",
                expires_at=access_expires,
                reason="logout"
            )
            
            # Blacklist refresh token if provided
            if refresh_token:
                try:
                    refresh_payload = self.jwt_service.validate_token(refresh_token)
                    refresh_expires = self.blacklist_service.calculate_token_expiry("refresh")
                    await self.blacklist_service.add_to_blacklist(
                        token_jti=refresh_payload.jti or f"refresh_{token_payload.sub}_{int(datetime.utcnow().timestamp())}",
                        token_hash=self._hash_token(refresh_token),
                        user_id=token_payload.sub,
                        token_type="refresh",
                        expires_at=refresh_expires,
                        reason="logout"
                    )
                except Exception as e:
                    logger.warning(f"Failed to blacklist refresh token: {e}")
            
            logger.info(f"User logged out: {token_payload.sub}")
            return True
            
        except Exception as e:
            logger.error(f"Logout failed: {e}")
            return False

    async def get_current_user(self, access_token: str) -> UserMeResponse:
        """
        Get current user information from access token.

        Args:
            access_token: Valid access token

        Returns:
            UserMeResponse with user and organization info

        Raises:
            InvalidTokenException: If token is invalid
            TokenBlacklistedException: If token is blacklisted
        """
        try:
            # Validate token
            token_payload = self.jwt_service.validate_token(access_token)

            if token_payload.token_type != TokenType.ACCESS:
                raise InvalidTokenException("Invalid token type")

            # Check if token is blacklisted
            token_hash = self._hash_token(access_token)
            if await self.blacklist_service.is_blacklisted(
                token_jti=token_payload.jti,
                token_hash=token_hash
            ):
                raise TokenBlacklistedException()

            # Get user
            user = await User.find_one({"user_id": token_payload.sub})
            if not user or not user.is_active:
                raise InvalidTokenException("User not found or inactive")

            # Get organization relationship
            user_org = await UserOrganization.find_one({
                "user_id": user.user_id,
                "organization_id": token_payload.organization_id,
                "is_active": True
            })

            if not user_org:
                raise InvalidTokenException("Organization relationship not found")

            # Get organization
            organization = await Organization.find_one({
                "organization_id": token_payload.organization_id
            })

            if not organization:
                raise InvalidTokenException("Organization not found")

            # Create response
            user_profile = UserProfileResponse(**user.to_profile_dict())
            org_response = OrganizationResponse(
                organization_id=organization.organization_id,
                name=organization.name,
                slug=organization.slug,
                description=organization.description,
                is_active=organization.is_active,
                role=user_org.role,
                joined_at=user_org.joined_at.isoformat()
            )

            # Get user permissions
            permissions = self._get_user_permissions(user_org.role)

            return UserMeResponse(
                user=user_profile,
                organization=org_response,
                permissions=permissions
            )

        except JWTError as e:
            logger.warning(f"Invalid access token: {e}")
            raise InvalidTokenException(str(e))
        except Exception as e:
            logger.error(f"Get current user failed: {e}")
            raise InvalidTokenException("Failed to get user information")

    async def _create_login_response(
        self,
        user: User,
        organization: Organization,
        user_org: UserOrganization
    ) -> LoginResponse:
        """Create login response with tokens and user info."""
        # Generate tokens
        access_token = self.jwt_service.create_access_token(
            user_id=user.user_id,
            organization_id=organization.organization_id,
            role=user_org.role,
            email=user.email
        )

        refresh_token = self.jwt_service.create_refresh_token(user_id=user.user_id)

        # Create response models
        user_profile = UserProfileResponse(**user.to_profile_dict())
        org_response = OrganizationResponse(
            organization_id=organization.organization_id,
            name=organization.name,
            slug=organization.slug,
            description=organization.description,
            is_active=organization.is_active,
            role=user_org.role,
            joined_at=user_org.joined_at.isoformat()
        )

        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=self.access_token_expire_minutes * 60,
            user=user_profile,
            organization=org_response
        )

    def _hash_token(self, token: str) -> str:
        """Create SHA256 hash of token for blacklist storage."""
        return hashlib.sha256(token.encode()).hexdigest()

    def _get_user_permissions(self, role: str) -> list[str]:
        """Get list of permissions for a user role."""
        role_permissions = {
            Role.USER.value: [
                "read:projects",
                "create:projects",
                "update:own_projects",
                "delete:own_projects",
                "read:test_cases",
                "create:test_cases",
                "update:own_test_cases",
                "delete:own_test_cases",
                "execute:tests",
            ],
            Role.ORG_ADMIN.value: [
                "read:projects",
                "create:projects",
                "update:all_projects",
                "delete:all_projects",
                "read:test_cases",
                "create:test_cases",
                "update:all_test_cases",
                "delete:all_test_cases",
                "execute:tests",
                "manage:users",
                "manage:organization_settings",
                "read:analytics",
            ],
            Role.ADMIN.value: [
                "*",  # All permissions
            ]
        }

        return role_permissions.get(role, [])
