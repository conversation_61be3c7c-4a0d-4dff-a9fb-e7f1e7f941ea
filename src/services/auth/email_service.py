"""
Email Validation Service for QAK Authentication

Handles email format validation, uniqueness checking, and domain validation.
Supports configurable email policies and domain restrictions.
"""

import re
import os
from typing import List, Optional, Set
from email_validator import validate_email, EmailNotValidError
from pydantic import BaseModel
from src.database.models.user import User
from src.exceptions.auth_exceptions import EmailAlreadyExistsException
import logging

logger = logging.getLogger(__name__)


class EmailValidationResult(BaseModel):
    """Result of email validation."""
    
    is_valid: bool
    normalized_email: Optional[str] = None
    errors: List[str] = []
    warnings: List[str] = []


class EmailPolicy(BaseModel):
    """Email validation policy configuration."""
    
    # Domain restrictions
    allowed_domains: Optional[Set[str]] = None  # If set, only these domains are allowed
    blocked_domains: Set[str] = set()  # Domains that are explicitly blocked
    
    # Format restrictions
    allow_smtputf8: bool = True  # Allow international characters
    check_deliverability: bool = True  # Check if domain has MX record
    
    # Business rules
    require_corporate_email: bool = False  # Require corporate (non-free) email
    max_length: int = 254  # RFC 5321 limit
    
    @classmethod
    def from_env(cls) -> "EmailPolicy":
        """Create email policy from environment variables."""
        # Parse allowed domains from environment
        allowed_domains_str = os.getenv("EMAIL_ALLOWED_DOMAINS", "")
        allowed_domains = None
        if allowed_domains_str:
            allowed_domains = set(domain.strip().lower() for domain in allowed_domains_str.split(","))
        
        # Parse blocked domains from environment
        blocked_domains_str = os.getenv("EMAIL_BLOCKED_DOMAINS", "")
        blocked_domains = set()
        if blocked_domains_str:
            blocked_domains = set(domain.strip().lower() for domain in blocked_domains_str.split(","))
        
        return cls(
            allowed_domains=allowed_domains,
            blocked_domains=blocked_domains,
            require_corporate_email=os.getenv("EMAIL_REQUIRE_CORPORATE", "false").lower() == "true",
            check_deliverability=os.getenv("EMAIL_CHECK_DELIVERABILITY", "true").lower() == "true",
        )


class EmailService:
    """Email validation and management service."""
    
    # Common free email providers
    FREE_EMAIL_DOMAINS = {
        "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "aol.com",
        "icloud.com", "mail.com", "protonmail.com", "tutanota.com",
        "yandex.com", "mail.ru", "qq.com", "163.com", "126.com",
        "sina.com", "sohu.com", "live.com", "msn.com", "yahoo.co.uk",
        "yahoo.ca", "yahoo.com.au", "gmx.com", "web.de", "t-online.de"
    }
    
    def __init__(self, policy: EmailPolicy = None):
        """
        Initialize email service.
        
        Args:
            policy: Email validation policy (defaults to environment-based policy)
        """
        self.policy = policy or EmailPolicy.from_env()
        logger.info("Email service initialized")
        logger.info(f"Email policy: corporate_required={self.policy.require_corporate_email}, "
                   f"check_deliverability={self.policy.check_deliverability}")
    
    async def validate_email(self, email: str) -> EmailValidationResult:
        """
        Comprehensive email validation.
        
        Args:
            email: Email address to validate
            
        Returns:
            EmailValidationResult with validation details
        """
        errors = []
        warnings = []
        normalized_email = None
        
        if not email:
            return EmailValidationResult(
                is_valid=False,
                errors=["Email address is required"]
            )
        
        # Basic format validation using email-validator
        try:
            validation_result = validate_email(
                email,
                check_deliverability=self.policy.check_deliverability,
                allow_smtputf8=self.policy.allow_smtputf8
            )
            normalized_email = validation_result.email
            
        except EmailNotValidError as e:
            errors.append(f"Invalid email format: {str(e)}")
            return EmailValidationResult(
                is_valid=False,
                errors=errors
            )
        
        # Length validation
        if len(normalized_email) > self.policy.max_length:
            errors.append(f"Email address too long (max {self.policy.max_length} characters)")
        
        # Extract domain for domain-based validations
        domain = normalized_email.split('@')[1].lower()
        
        # Domain restrictions
        if self.policy.allowed_domains and domain not in self.policy.allowed_domains:
            errors.append(f"Email domain '{domain}' is not allowed")
        
        if domain in self.policy.blocked_domains:
            errors.append(f"Email domain '{domain}' is blocked")
        
        # Corporate email requirement
        if self.policy.require_corporate_email and domain in self.FREE_EMAIL_DOMAINS:
            errors.append("Corporate email address required (free email providers not allowed)")
        
        # Add warnings for free email providers (even if allowed)
        if domain in self.FREE_EMAIL_DOMAINS and not self.policy.require_corporate_email:
            warnings.append("Using free email provider")
        
        is_valid = len(errors) == 0
        
        return EmailValidationResult(
            is_valid=is_valid,
            normalized_email=normalized_email if is_valid else None,
            errors=errors,
            warnings=warnings
        )
    
    async def check_email_uniqueness(self, email: str, exclude_user_id: Optional[str] = None) -> bool:
        """
        Check if email address is unique in the system.
        
        Args:
            email: Email address to check
            exclude_user_id: User ID to exclude from check (for updates)
            
        Returns:
            True if email is unique, False otherwise
        """
        try:
            # Normalize email for consistent checking
            validation_result = await self.validate_email(email)
            if not validation_result.is_valid:
                return False
            
            normalized_email = validation_result.normalized_email
            
            # Build query
            query = {"email": normalized_email}
            if exclude_user_id:
                query["user_id"] = {"$ne": exclude_user_id}
            
            # Check if user exists with this email
            existing_user = await User.find_one(query)
            
            is_unique = existing_user is None
            
            if not is_unique:
                logger.debug(f"Email {normalized_email} is already registered")
            
            return is_unique
            
        except Exception as e:
            logger.error(f"Error checking email uniqueness: {e}")
            return False
    
    async def validate_and_check_uniqueness(
        self, 
        email: str, 
        exclude_user_id: Optional[str] = None
    ) -> EmailValidationResult:
        """
        Validate email format and check uniqueness.
        
        Args:
            email: Email address to validate
            exclude_user_id: User ID to exclude from uniqueness check
            
        Returns:
            EmailValidationResult with validation and uniqueness check
        """
        # First validate format
        result = await self.validate_email(email)
        
        if not result.is_valid:
            return result
        
        # Then check uniqueness
        is_unique = await self.check_email_uniqueness(result.normalized_email, exclude_user_id)
        
        if not is_unique:
            result.is_valid = False
            result.errors.append("Email address is already registered")
        
        return result
    
    def extract_domain(self, email: str) -> Optional[str]:
        """
        Extract domain from email address.
        
        Args:
            email: Email address
            
        Returns:
            Domain part of email or None if invalid
        """
        try:
            if '@' in email:
                return email.split('@')[1].lower()
            return None
        except Exception:
            return None
    
    def is_free_email_domain(self, email: str) -> bool:
        """
        Check if email uses a free email provider.
        
        Args:
            email: Email address to check
            
        Returns:
            True if using free email provider, False otherwise
        """
        domain = self.extract_domain(email)
        return domain in self.FREE_EMAIL_DOMAINS if domain else False
    
    def suggest_corporate_alternatives(self, email: str) -> List[str]:
        """
        Suggest corporate email alternatives for free email addresses.
        
        Args:
            email: Email address
            
        Returns:
            List of suggested corporate email formats
        """
        if not self.is_free_email_domain(email):
            return []
        
        local_part = email.split('@')[0]
        
        # Common corporate domain patterns
        suggestions = [
            f"{local_part}@company.com",
            f"{local_part}@yourcompany.com",
            f"{local_part}@organization.org",
        ]
        
        return suggestions
    
    async def get_email_statistics(self) -> dict:
        """
        Get email usage statistics.
        
        Returns:
            Dictionary with email statistics
        """
        try:
            total_users = await User.count()
            
            # Count users by domain type
            free_email_count = 0
            corporate_email_count = 0
            
            # This is a simplified implementation
            # In production, you might want to use aggregation pipeline
            async for user in User.find():
                if self.is_free_email_domain(user.email):
                    free_email_count += 1
                else:
                    corporate_email_count += 1
            
            return {
                "total_users": total_users,
                "free_email_users": free_email_count,
                "corporate_email_users": corporate_email_count,
                "free_email_percentage": (free_email_count / total_users * 100) if total_users > 0 else 0,
            }
            
        except Exception as e:
            logger.error(f"Failed to get email statistics: {e}")
            return {
                "total_users": 0,
                "free_email_users": 0,
                "corporate_email_users": 0,
                "free_email_percentage": 0,
            }
