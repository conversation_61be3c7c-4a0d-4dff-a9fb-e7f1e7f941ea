"""
Organization Management Service for QAK Multi-Tenant Architecture

Handles organization creation, user management, and organization-level operations.
Provides comprehensive organization management for multi-tenant system.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from src.database.models.organization import Organization
from src.database.models.user import User
from src.database.models.user_organization import UserOrganization, Role
from src.exceptions.auth_exceptions import (
    OrganizationNotFoundException, UserNotFoundException,
    OrganizationAccessDeniedException, InsufficientPermissionsException
)
import logging

logger = logging.getLogger(__name__)


class OrganizationService:
    """Service for managing organizations and user relationships."""
    
    def __init__(self):
        """Initialize organization service."""
        logger.info("Organization service initialized")
    
    async def create_organization(
        self,
        name: str,
        description: str = "",
        creator_user_id: Optional[str] = None,
        settings: Optional[Dict[str, Any]] = None
    ) -> Organization:
        """
        Create a new organization.
        
        Args:
            name: Organization name
            description: Organization description
            creator_user_id: User ID of the creator (will be made ORG_ADMIN)
            settings: Organization settings
            
        Returns:
            Created Organization instance
            
        Raises:
            ValueError: If organization name already exists
        """
        logger.info(f"Creating organization: {name}")
        
        # Check if organization name already exists
        existing_org = await Organization.find_one({"name": name})
        if existing_org:
            raise ValueError(f"Organization with name '{name}' already exists")
        
        # Create organization
        organization = Organization.create_with_auto_slug(
            name=name,
            description=description,
            settings=settings or {}
        )
        await organization.insert()
        
        # Add creator as ORG_ADMIN if provided
        if creator_user_id:
            await self.add_user_to_organization(
                user_id=creator_user_id,
                organization_id=organization.organization_id,
                role=Role.ORG_ADMIN
            )
        
        logger.info(f"Organization created: {organization.organization_id}")
        return organization
    
    async def get_organization_by_id(self, organization_id: str) -> Optional[Organization]:
        """
        Get organization by ID.
        
        Args:
            organization_id: Organization identifier
            
        Returns:
            Organization instance or None if not found
        """
        return await Organization.find_one({"organization_id": organization_id})
    
    async def get_organization_by_slug(self, slug: str) -> Optional[Organization]:
        """
        Get organization by slug.
        
        Args:
            slug: Organization slug
            
        Returns:
            Organization instance or None if not found
        """
        return await Organization.find_one({"slug": slug})
    
    async def get_organizations_for_user(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all organizations for a user with their roles.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of organization dictionaries with role information
        """
        # Get all user-organization relationships for the user
        user_orgs = await UserOrganization.find({
            "user_id": user_id,
            "is_active": True
        }).to_list()
        
        organizations = []
        for user_org in user_orgs:
            # Get organization details
            org = await self.get_organization_by_id(user_org.organization_id)
            if org and org.is_active:
                org_dict = org.to_dict()
                org_dict["role"] = user_org.role
                org_dict["joined_at"] = user_org.joined_at.isoformat()
                org_dict["last_accessed"] = user_org.last_accessed.isoformat() if user_org.last_accessed else None
                organizations.append(org_dict)
        
        return organizations
    
    async def add_user_to_organization(
        self,
        user_id: str,
        organization_id: str,
        role: Role = Role.USER,
        added_by_user_id: Optional[str] = None
    ) -> UserOrganization:
        """
        Add a user to an organization with specified role.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            role: User role in the organization
            added_by_user_id: User ID of who is adding (for permission check)
            
        Returns:
            Created UserOrganization relationship
            
        Raises:
            UserNotFoundException: If user doesn't exist
            OrganizationNotFoundException: If organization doesn't exist
            InsufficientPermissionsException: If added_by_user lacks permissions
        """
        logger.info(f"Adding user {user_id} to organization {organization_id} with role {role.value}")
        
        # Verify user exists
        user = await User.find_one({"user_id": user_id})
        if not user:
            raise UserNotFoundException(user_id)
        
        # Verify organization exists
        organization = await self.get_organization_by_id(organization_id)
        if not organization:
            raise OrganizationNotFoundException(organization_id)
        
        # Check permissions if added_by_user_id is provided
        if added_by_user_id:
            can_add = await self.can_user_manage_organization_users(
                user_id=added_by_user_id,
                organization_id=organization_id
            )
            if not can_add:
                raise InsufficientPermissionsException("manage:users")
        
        # Check if relationship already exists
        existing_relationship = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id
        })
        
        if existing_relationship:
            if existing_relationship.is_active:
                # Update role if different
                if existing_relationship.role != role.value:
                    existing_relationship.change_role(role)
                    await existing_relationship.save()
                    logger.info(f"Updated user role in organization: {user_id} -> {role.value}")
                return existing_relationship
            else:
                # Reactivate relationship
                existing_relationship.activate()
                existing_relationship.change_role(role)
                await existing_relationship.save()
                logger.info(f"Reactivated user in organization: {user_id}")
                return existing_relationship
        
        # Create new relationship
        user_org = UserOrganization(
            user_id=user_id,
            organization_id=organization_id,
            role=role.value,
            is_active=True
        )
        await user_org.insert()
        
        logger.info(f"User added to organization: {user_id} -> {organization_id}")
        return user_org
    
    async def remove_user_from_organization(
        self,
        user_id: str,
        organization_id: str,
        removed_by_user_id: Optional[str] = None
    ) -> bool:
        """
        Remove a user from an organization.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            removed_by_user_id: User ID of who is removing (for permission check)
            
        Returns:
            True if user was removed, False if not found
            
        Raises:
            InsufficientPermissionsException: If removed_by_user lacks permissions
        """
        logger.info(f"Removing user {user_id} from organization {organization_id}")
        
        # Check permissions if removed_by_user_id is provided
        if removed_by_user_id and removed_by_user_id != user_id:
            can_remove = await self.can_user_manage_organization_users(
                user_id=removed_by_user_id,
                organization_id=organization_id
            )
            if not can_remove:
                raise InsufficientPermissionsException("manage:users")
        
        # Find and deactivate relationship
        user_org = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id,
            "is_active": True
        })
        
        if user_org:
            user_org.deactivate()
            await user_org.save()
            logger.info(f"User removed from organization: {user_id}")
            return True
        
        return False
    
    async def update_user_role(
        self,
        user_id: str,
        organization_id: str,
        new_role: Role,
        updated_by_user_id: Optional[str] = None
    ) -> bool:
        """
        Update a user's role in an organization.
        
        Args:
            user_id: User identifier
            organization_id: Organization identifier
            new_role: New role for the user
            updated_by_user_id: User ID of who is updating (for permission check)
            
        Returns:
            True if role was updated, False if user not found in organization
            
        Raises:
            InsufficientPermissionsException: If updated_by_user lacks permissions
        """
        logger.info(f"Updating user {user_id} role to {new_role.value} in organization {organization_id}")
        
        # Check permissions if updated_by_user_id is provided
        if updated_by_user_id and updated_by_user_id != user_id:
            can_update = await self.can_user_manage_organization_users(
                user_id=updated_by_user_id,
                organization_id=organization_id
            )
            if not can_update:
                raise InsufficientPermissionsException("manage:users")
        
        # Find and update relationship
        user_org = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id,
            "is_active": True
        })
        
        if user_org:
            user_org.change_role(new_role)
            await user_org.save()
            logger.info(f"User role updated: {user_id} -> {new_role.value}")
            return True
        
        return False

    async def get_organization_users(
        self,
        organization_id: str,
        include_inactive: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get all users in an organization with their roles.

        Args:
            organization_id: Organization identifier
            include_inactive: Whether to include inactive relationships

        Returns:
            List of user dictionaries with role information
        """
        # Build query
        query = {"organization_id": organization_id}
        if not include_inactive:
            query["is_active"] = True

        # Get all user-organization relationships
        user_orgs = await UserOrganization.find(query).to_list()

        users = []
        for user_org in user_orgs:
            # Get user details
            user = await User.find_one({"user_id": user_org.user_id})
            if user:
                user_dict = user.to_profile_dict()
                user_dict["role"] = user_org.role
                user_dict["joined_at"] = user_org.joined_at.isoformat()
                user_dict["last_accessed"] = user_org.last_accessed.isoformat() if user_org.last_accessed else None
                user_dict["is_active_in_org"] = user_org.is_active
                users.append(user_dict)

        return users

    async def can_user_access_organization(
        self,
        user_id: str,
        organization_id: str
    ) -> bool:
        """
        Check if a user has access to an organization.

        Args:
            user_id: User identifier
            organization_id: Organization identifier

        Returns:
            True if user has access, False otherwise
        """
        user_org = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id,
            "is_active": True
        })

        return user_org is not None

    async def can_user_manage_organization_users(
        self,
        user_id: str,
        organization_id: str
    ) -> bool:
        """
        Check if a user can manage other users in an organization.

        Args:
            user_id: User identifier
            organization_id: Organization identifier

        Returns:
            True if user can manage users, False otherwise
        """
        user_org = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id,
            "is_active": True
        })

        if not user_org:
            return False

        # Only ORG_ADMIN and ADMIN can manage users
        return user_org.role in [Role.ORG_ADMIN.value, Role.ADMIN.value]

    async def get_user_role_in_organization(
        self,
        user_id: str,
        organization_id: str
    ) -> Optional[Role]:
        """
        Get a user's role in an organization.

        Args:
            user_id: User identifier
            organization_id: Organization identifier

        Returns:
            User's role or None if not in organization
        """
        user_org = await UserOrganization.find_one({
            "user_id": user_id,
            "organization_id": organization_id,
            "is_active": True
        })

        if user_org:
            return Role(user_org.role)

        return None
