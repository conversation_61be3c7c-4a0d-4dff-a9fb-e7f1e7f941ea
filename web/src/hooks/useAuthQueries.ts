'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  loginUser, 
  registerUser, 
  getCurrentUser, 
  logoutUser,
  refreshAccessToken,
  switchOrganization,
  checkPasswordStrength,
  getUserOrganizations,
  getOrganization,
  updateOrganization,
  getOrganizationUsers,
  inviteUserToOrganization,
  updateUserRole,
  removeUserFromOrganization,
  type LoginRequest,
  type RegisterRequest,
  type RefreshTokenRequest,
  type AuthResponse,
  type UserProfileResponse,
  type AuthOrganization,
  tokenManager,
} from '@/lib/api';

// Query keys
export const authQueryKeys = {
  all: ['auth'] as const,
  user: () => [...authQueryKeys.all, 'user'] as const,
  organizations: () => [...authQueryKeys.all, 'organizations'] as const,
  organization: (id: string) => [...authQueryKeys.organizations(), id] as const,
  organizationUsers: (id: string) => [...authQueryKeys.organization(id), 'users'] as const,
  passwordStrength: (password: string) => [...authQueryKeys.all, 'password-strength', password] as const,
};

// ==========================================
// Authentication Hooks
// ==========================================

// Get current user profile
export function useCurrentUser() {
  return useQuery({
    queryKey: authQueryKeys.user(),
    queryFn: getCurrentUser,
    enabled: !!tokenManager.getAccessToken(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 401 errors
      if (error instanceof Error && error.message.includes('Authentication required')) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

// Login mutation
export function useLogin() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: LoginRequest) => loginUser(data),
    onSuccess: (response: AuthResponse) => {
      // Store tokens
      tokenManager.setTokens(response.access_token, response.refresh_token);
      
      // Store user data
      const userData = {
        user: response.user,
        organization: response.organization,
        user_organization: response.user_organization,
        permissions: [], // Will be populated by getCurrentUser
      };
      localStorage.setItem('qak_user_data', JSON.stringify(userData));
      
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: authQueryKeys.user() });
      queryClient.invalidateQueries({ queryKey: authQueryKeys.organizations() });
    },
    onError: (error) => {
      console.error('Login failed:', error);
      tokenManager.clearTokens();
    },
  });
}

// Register mutation
export function useRegister() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: RegisterRequest) => registerUser(data),
    onSuccess: (response: AuthResponse) => {
      // Store tokens
      tokenManager.setTokens(response.access_token, response.refresh_token);
      
      // Store user data
      const userData = {
        user: response.user,
        organization: response.organization,
        user_organization: response.user_organization,
        permissions: [], // Will be populated by getCurrentUser
      };
      localStorage.setItem('qak_user_data', JSON.stringify(userData));
      
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: authQueryKeys.user() });
      queryClient.invalidateQueries({ queryKey: authQueryKeys.organizations() });
    },
    onError: (error) => {
      console.error('Registration failed:', error);
      tokenManager.clearTokens();
    },
  });
}

// Logout mutation
export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const refreshToken = tokenManager.getRefreshToken();
      if (refreshToken) {
        await logoutUser(refreshToken);
      }
    },
    onSettled: () => {
      // Clear all auth data regardless of API call success
      tokenManager.clearTokens();
      localStorage.removeItem('qak_user_data');
      
      // Clear all cached data
      queryClient.clear();
    },
  });
}

// Switch organization mutation
export function useSwitchOrganization() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (organizationId: string) => switchOrganization(organizationId),
    onSuccess: (response: AuthResponse) => {
      // Update tokens
      tokenManager.setTokens(response.access_token, response.refresh_token);
      
      // Update user data
      const userData = {
        user: response.user,
        organization: response.organization,
        user_organization: response.user_organization,
        permissions: [], // Will be populated by getCurrentUser
      };
      localStorage.setItem('qak_user_data', JSON.stringify(userData));
      
      // Invalidate all cached data since organization context changed
      queryClient.clear();
    },
  });
}

// Password strength check
export function usePasswordStrength(password: string) {
  return useQuery({
    queryKey: authQueryKeys.passwordStrength(password),
    queryFn: () => checkPasswordStrength(password),
    enabled: password.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// ==========================================
// Organization Management Hooks
// ==========================================

// Get user's organizations
export function useUserOrganizations() {
  return useQuery({
    queryKey: authQueryKeys.organizations(),
    queryFn: getUserOrganizations,
    enabled: !!tokenManager.getAccessToken(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get organization details
export function useOrganization(organizationId: string) {
  return useQuery({
    queryKey: authQueryKeys.organization(organizationId),
    queryFn: () => getOrganization(organizationId),
    enabled: !!organizationId && !!tokenManager.getAccessToken(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Update organization mutation
export function useUpdateOrganization() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      organizationId, 
      data 
    }: { 
      organizationId: string; 
      data: Partial<Pick<AuthOrganization, 'name' | 'description' | 'settings'>>; 
    }) => updateOrganization(organizationId, data),
    onSuccess: (updatedOrg) => {
      // Update organization cache
      queryClient.setQueryData(
        authQueryKeys.organization(updatedOrg.organization_id),
        updatedOrg
      );
      
      // Invalidate organizations list
      queryClient.invalidateQueries({ queryKey: authQueryKeys.organizations() });
      
      // Update user data if this is the current organization
      const userData = localStorage.getItem('qak_user_data');
      if (userData) {
        try {
          const user = JSON.parse(userData);
          if (user.organization.organization_id === updatedOrg.organization_id) {
            user.organization = updatedOrg;
            localStorage.setItem('qak_user_data', JSON.stringify(user));
          }
        } catch (error) {
          console.warn('Failed to update user data:', error);
        }
      }
    },
  });
}

// Get organization users
export function useOrganizationUsers(organizationId: string) {
  return useQuery({
    queryKey: authQueryKeys.organizationUsers(organizationId),
    queryFn: () => getOrganizationUsers(organizationId),
    enabled: !!organizationId && !!tokenManager.getAccessToken(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Invite user to organization mutation
export function useInviteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      organizationId, 
      email, 
      role 
    }: { 
      organizationId: string; 
      email: string; 
      role: 'USER' | 'ORG_ADMIN'; 
    }) => inviteUserToOrganization(organizationId, { email, role }),
    onSuccess: (_, variables) => {
      // Invalidate organization users to refetch the list
      queryClient.invalidateQueries({ 
        queryKey: authQueryKeys.organizationUsers(variables.organizationId) 
      });
    },
  });
}

// Update user role mutation
export function useUpdateUserRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      organizationId, 
      userId, 
      role 
    }: { 
      organizationId: string; 
      userId: string; 
      role: 'USER' | 'ORG_ADMIN'; 
    }) => updateUserRole(organizationId, userId, role),
    onSuccess: (_, variables) => {
      // Invalidate organization users to refetch the list
      queryClient.invalidateQueries({ 
        queryKey: authQueryKeys.organizationUsers(variables.organizationId) 
      });
    },
  });
}

// Remove user from organization mutation
export function useRemoveUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      organizationId, 
      userId 
    }: { 
      organizationId: string; 
      userId: string; 
    }) => removeUserFromOrganization(organizationId, userId),
    onSuccess: (_, variables) => {
      // Invalidate organization users to refetch the list
      queryClient.invalidateQueries({ 
        queryKey: authQueryKeys.organizationUsers(variables.organizationId) 
      });
    },
  });
}
