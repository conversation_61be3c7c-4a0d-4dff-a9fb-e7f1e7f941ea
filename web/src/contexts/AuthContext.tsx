'use client';

import React, { create<PERSON>ontext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

// Authentication types
export interface User {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Organization {
  organization_id: string;
  name: string;
  slug: string;
  description?: string;
  settings: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface UserOrganization {
  user_id: string;
  organization_id: string;
  role: 'USER' | 'ORG_ADMIN' | 'ADMIN';
  joined_at: string;
}

export interface AuthUser {
  user: User;
  organization: Organization;
  user_organization: UserOrganization;
  permissions: string[];
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  organization_name: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
  organization: Organization;
  user_organization: UserOrganization;
}

// Auth context interface
interface AuthContextType {
  // State
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  switchOrganization: (organizationId: string) => Promise<void>;
  
  // Token management
  getAccessToken: () => string | null;
  isTokenExpired: () => boolean;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Token storage keys
const ACCESS_TOKEN_KEY = 'qak_access_token';
const REFRESH_TOKEN_KEY = 'qak_refresh_token';
const USER_DATA_KEY = 'qak_user_data';

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Check if user is authenticated
  const isAuthenticated = !!user;

  // Get access token from localStorage
  const getAccessToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(ACCESS_TOKEN_KEY);
  };

  // Get refresh token from localStorage
  const getRefreshToken = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(REFRESH_TOKEN_KEY);
  };

  // Check if token is expired
  const isTokenExpired = (): boolean => {
    const token = getAccessToken();
    if (!token) return true;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  };

  // Store tokens and user data
  const storeAuthData = (authResponse: AuthResponse) => {
    if (typeof window === 'undefined') return;

    localStorage.setItem(ACCESS_TOKEN_KEY, authResponse.access_token);
    localStorage.setItem(REFRESH_TOKEN_KEY, authResponse.refresh_token);
    
    const userData: AuthUser = {
      user: authResponse.user,
      organization: authResponse.organization,
      user_organization: authResponse.user_organization,
      permissions: [] // Will be populated from /auth/me endpoint
    };
    
    localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
    setUser(userData);
  };

  // Clear auth data
  const clearAuthData = () => {
    if (typeof window === 'undefined') return;

    localStorage.removeItem(ACCESS_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_DATA_KEY);
    setUser(null);
  };

  // Login function
  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Login failed');
      }

      const authResponse: AuthResponse = await response.json();
      storeAuthData(authResponse);
      
      // Fetch user profile with permissions
      await fetchUserProfile();
      
      router.push('/dashboard');
    } catch (error) {
      clearAuthData();
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (data: RegisterData): Promise<void> => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Registration failed');
      }

      const authResponse: AuthResponse = await response.json();
      storeAuthData(authResponse);
      
      // Fetch user profile with permissions
      await fetchUserProfile();
      
      router.push('/dashboard');
    } catch (error) {
      clearAuthData();
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      const refreshToken = getRefreshToken();
      const accessToken = getAccessToken();

      if (refreshToken && accessToken) {
        // Call logout endpoint to blacklist tokens
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
          },
          body: JSON.stringify({ refresh_token: refreshToken }),
        });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      clearAuthData();
      router.push('/auth/login');
    }
  };

  // Refresh token function
  const refreshToken = async (): Promise<void> => {
    try {
      const refreshTokenValue = getRefreshToken();
      if (!refreshTokenValue) {
        throw new Error('No refresh token available');
      }

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshTokenValue }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const authResponse: AuthResponse = await response.json();
      storeAuthData(authResponse);
    } catch (error) {
      clearAuthData();
      router.push('/auth/login');
      throw error;
    }
  };

  // Fetch user profile with permissions
  const fetchUserProfile = async (): Promise<void> => {
    try {
      const accessToken = getAccessToken();
      if (!accessToken) return;

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }

      const profileData = await response.json();
      
      const userData: AuthUser = {
        user: profileData.user,
        organization: profileData.organization,
        user_organization: profileData.user_organization || {
          user_id: profileData.user.user_id,
          organization_id: profileData.organization.organization_id,
          role: 'USER',
          joined_at: new Date().toISOString()
        },
        permissions: profileData.permissions || []
      };

      localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
      setUser(userData);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      clearAuthData();
    }
  };

  // Switch organization
  const switchOrganization = async (organizationId: string): Promise<void> => {
    try {
      setIsLoading(true);
      
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error('No access token available');
      }

      const response = await fetch('/api/auth/switch-organization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ organization_id: organizationId }),
      });

      if (!response.ok) {
        throw new Error('Failed to switch organization');
      }

      const authResponse: AuthResponse = await response.json();
      storeAuthData(authResponse);
      
      // Fetch updated user profile
      await fetchUserProfile();
      
      // Refresh the page to update organization context
      window.location.reload();
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        
        // Check if we have stored user data
        const storedUserData = localStorage.getItem(USER_DATA_KEY);
        const accessToken = getAccessToken();
        
        if (!storedUserData || !accessToken) {
          setIsLoading(false);
          return;
        }

        // Check if token is expired
        if (isTokenExpired()) {
          // Try to refresh token
          try {
            await refreshToken();
          } catch {
            clearAuthData();
            setIsLoading(false);
            return;
          }
        }

        // Set user data and fetch fresh profile
        const userData: AuthUser = JSON.parse(storedUserData);
        setUser(userData);
        
        // Fetch fresh user profile in background
        await fetchUserProfile();
      } catch (error) {
        console.error('Auth initialization failed:', error);
        clearAuthData();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
    switchOrganization,
    getAccessToken,
    isTokenExpired,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
