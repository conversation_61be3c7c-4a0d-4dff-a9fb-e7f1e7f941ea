'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Settings, 
  Building2, 
  Save, 
  Loader2, 
  ArrowLeft,
  AlertTriangle,
  Info
} from 'lucide-react';
import Link from 'next/link';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useUpdateOrganization } from '@/hooks/useAuthQueries';

// Validation schema
const organizationSettingsSchema = z.object({
  name: z
    .string()
    .min(1, 'Organization name is required')
    .min(2, 'Organization name must be at least 2 characters')
    .max(100, 'Organization name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
});

type OrganizationSettingsFormData = z.infer<typeof organizationSettingsSchema>;

function OrganizationSettings() {
  const { user } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const updateOrganizationMutation = useUpdateOrganization();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty },
    reset,
  } = useForm<OrganizationSettingsFormData>({
    resolver: zodResolver(organizationSettingsSchema),
    defaultValues: {
      name: user?.organization.name || '',
      description: user?.organization.description || '',
    },
  });

  if (!user) return null;

  const organization = user.organization;
  const currentUserRole = user.user_organization.role;
  const canEditSettings = currentUserRole === 'ORG_ADMIN' || currentUserRole === 'ADMIN';

  const onSubmit = async (data: OrganizationSettingsFormData) => {
    if (!canEditSettings) {
      setError('You do not have permission to edit organization settings.');
      return;
    }

    try {
      setError(null);
      setSuccess(null);

      await updateOrganizationMutation.mutateAsync({
        organizationId: organization.organization_id,
        data: {
          name: data.name.trim(),
          description: data.description?.trim() || undefined,
        },
      });

      setSuccess('Organization settings updated successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update organization settings. Please try again.';
      setError(errorMessage);
    }
  };

  const handleReset = () => {
    reset({
      name: organization.name,
      description: organization.description || '',
    });
    setError(null);
    setSuccess(null);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/organization">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Organization
          </Button>
        </Link>
        
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">Organization Settings</h1>
          <p className="text-muted-foreground">
            Manage your organization's information and preferences
          </p>
        </div>
      </div>

      {!canEditSettings && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            You need Organization Admin or Admin permissions to edit these settings.
          </AlertDescription>
        </Alert>
      )}

      {/* Organization Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Organization Information</span>
          </CardTitle>
          <CardDescription>
            Update your organization's basic information and description
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="border-green-200 bg-green-50">
                <Save className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  {success}
                </AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Organization Name</Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter organization name"
                  disabled={isSubmitting || !canEditSettings}
                  {...register('name')}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Organization Slug</Label>
                <Input
                  id="slug"
                  type="text"
                  value={organization.slug}
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-muted-foreground">
                  The organization slug cannot be changed after creation
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your organization (optional)"
                rows={3}
                disabled={isSubmitting || !canEditSettings}
                {...register('description')}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                This description will be visible to organization members
              </p>
            </div>

            {canEditSettings && (
              <div className="flex items-center space-x-2">
                <Button
                  type="submit"
                  disabled={isSubmitting || !isDirty}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={handleReset}
                  disabled={isSubmitting || !isDirty}
                >
                  Reset
                </Button>
              </div>
            )}
          </form>
        </CardContent>
      </Card>

      {/* Organization Details */}
      <Card>
        <CardHeader>
          <CardTitle>Organization Details</CardTitle>
          <CardDescription>
            Read-only information about your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">
                Organization ID
              </Label>
              <div className="p-2 bg-gray-50 rounded border text-sm font-mono">
                {organization.organization_id}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">
                Created Date
              </Label>
              <div className="p-2 bg-gray-50 rounded border text-sm">
                {formatDate(organization.created_at)}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">
                Last Updated
              </Label>
              <div className="p-2 bg-gray-50 rounded border text-sm">
                {formatDate(organization.updated_at)}
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">
                Your Role
              </Label>
              <div className="p-2 bg-gray-50 rounded border text-sm">
                {currentUserRole}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      {currentUserRole === 'ADMIN' && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600 flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>Danger Zone</span>
            </CardTitle>
            <CardDescription>
              Irreversible and destructive actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                <h4 className="font-medium text-red-900 mb-2">Delete Organization</h4>
                <p className="text-sm text-red-700 mb-3">
                  Permanently delete this organization and all associated data. This action cannot be undone.
                </p>
                <Button variant="destructive" size="sm" disabled>
                  Delete Organization
                </Button>
                <p className="text-xs text-red-600 mt-2">
                  Organization deletion is not yet implemented
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default function OrganizationSettingsPage() {
  return (
    <ProtectedRoute requiredRole="ORG_ADMIN">
      <OrganizationSettings />
    </ProtectedRoute>
  );
}
