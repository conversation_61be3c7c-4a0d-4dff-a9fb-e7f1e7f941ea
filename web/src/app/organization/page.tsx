'use client';

import React, { useState } from 'react';
import { 
  Users, 
  Settings, 
  Plus, 
  Mail, 
  Shield, 
  MoreHorizontal,
  Calendar,
  Building2,
  User<PERSON><PERSON>ck,
  UserX,
  Crown
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganizationUsers, useUpdateUserRole, useRemoveUser } from '@/hooks/useAuthQueries';
import { InviteUserDialog } from '@/components/organization/InviteUserDialog';

function OrganizationDashboard() {
  const { user } = useAuth();
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  
  const { 
    data: usersData, 
    isLoading: usersLoading, 
    error: usersError 
  } = useOrganizationUsers(user?.organization.organization_id || '');
  
  const updateUserRoleMutation = useUpdateUserRole();
  const removeUserMutation = useRemoveUser();

  if (!user) return null;

  const organization = user.organization;
  const currentUserRole = user.user_organization.role;
  const canManageUsers = currentUserRole === 'ORG_ADMIN' || currentUserRole === 'ADMIN';

  const handleUpdateUserRole = async (userId: string, newRole: 'USER' | 'ORG_ADMIN') => {
    try {
      await updateUserRoleMutation.mutateAsync({
        organizationId: organization.organization_id,
        userId,
        role: newRole,
      });
    } catch (error) {
      console.error('Failed to update user role:', error);
    }
  };

  const handleRemoveUser = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this user from the organization?')) {
      return;
    }

    try {
      await removeUserMutation.mutateAsync({
        organizationId: organization.organization_id,
        userId,
      });
    } catch (error) {
      console.error('Failed to remove user:', error);
    }
  };

  const getUserInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'destructive';
      case 'ORG_ADMIN':
        return 'default';
      case 'USER':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Crown className="h-3 w-3" />;
      case 'ORG_ADMIN':
        return <Shield className="h-3 w-3" />;
      case 'USER':
        return <UserCheck className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">Organization Management</h1>
          <p className="text-muted-foreground">
            Manage your organization members and settings
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {canManageUsers && (
            <Button onClick={() => setInviteDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Invite User
            </Button>
          )}
          
          {(currentUserRole === 'ORG_ADMIN' || currentUserRole === 'ADMIN') && (
            <Button variant="outline" asChild>
              <a href="/organization/settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </a>
            </Button>
          )}
        </div>
      </div>

      {/* Organization Info */}
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <CardTitle>{organization.name}</CardTitle>
              <CardDescription>
                {organization.description || 'No description provided'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Created {formatDate(organization.created_at)}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {usersData?.users.length || 0} members
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Organization ID: {organization.organization_id}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Members List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Organization Members</span>
          </CardTitle>
          <CardDescription>
            Manage user roles and permissions within your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          {usersError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>
                Failed to load organization members. Please try again.
              </AlertDescription>
            </Alert>
          )}

          {usersLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-8 w-8" />
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {usersData?.users.map(({ user: orgUser, user_organization }) => (
                <div
                  key={orgUser.user_id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage 
                        src={`https://api.dicebear.com/7.x/initials/svg?seed=${orgUser.first_name}+${orgUser.last_name}`}
                        alt={`${orgUser.first_name} ${orgUser.last_name}`}
                      />
                      <AvatarFallback>
                        {getUserInitials(orgUser.first_name, orgUser.last_name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">
                          {orgUser.first_name} {orgUser.last_name}
                        </span>
                        {orgUser.user_id === user.user.user_id && (
                          <Badge variant="outline" className="text-xs">You</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {orgUser.email}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Joined {formatDate(user_organization.joined_at)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Badge 
                      variant={getRoleBadgeVariant(user_organization.role)}
                      className="flex items-center space-x-1"
                    >
                      {getRoleIcon(user_organization.role)}
                      <span>{user_organization.role}</span>
                    </Badge>

                    {canManageUsers && orgUser.user_id !== user.user.user_id && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {user_organization.role !== 'ORG_ADMIN' && (
                            <DropdownMenuItem
                              onClick={() => handleUpdateUserRole(orgUser.user_id, 'ORG_ADMIN')}
                              disabled={updateUserRoleMutation.isPending}
                            >
                              <Shield className="h-4 w-4 mr-2" />
                              Make Admin
                            </DropdownMenuItem>
                          )}
                          
                          {user_organization.role !== 'USER' && (
                            <DropdownMenuItem
                              onClick={() => handleUpdateUserRole(orgUser.user_id, 'USER')}
                              disabled={updateUserRoleMutation.isPending}
                            >
                              <UserCheck className="h-4 w-4 mr-2" />
                              Make User
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuSeparator />
                          
                          <DropdownMenuItem
                            onClick={() => handleRemoveUser(orgUser.user_id)}
                            disabled={removeUserMutation.isPending}
                            className="text-red-600 focus:text-red-600"
                          >
                            <UserX className="h-4 w-4 mr-2" />
                            Remove User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              ))}

              {usersData?.users.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No members found in this organization.</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invite User Dialog */}
      <InviteUserDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
        organizationId={organization.organization_id}
      />
    </div>
  );
}

export default function OrganizationPage() {
  return (
    <ProtectedRoute requiredRole="USER">
      <OrganizationDashboard />
    </ProtectedRoute>
  );
}
