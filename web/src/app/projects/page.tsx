"use client";

import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { getProjects } from '@/lib/api';
import type { Project } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, DevCard, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { FolderKanban, PlusCircle, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { ExportProjectButton } from '@/components/projects/ExportProjectButton';
import { ImportProjectBar } from '@/components/projects/ImportProjectBar';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function ProjectCard({ project }: { project: Project }) {
  return (
    <DevCard className="flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderKanban className="h-6 w-6 text-primary" />
          {project.name}
        </CardTitle>
        <CardDescription className="line-clamp-2">{project.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="mb-2">
          <span className="text-sm font-semibold">Tags:</span>
          <div className="flex flex-wrap gap-1 mt-1">
            {project.tags.length > 0 ? (
              project.tags.map((tag) => <Badge key={tag} variant="secondary">{tag}</Badge>)
            ) : (
              <span className="text-xs text-muted-foreground">No tags</span>
            )}
          </div>
        </div>
        <p className="text-xs text-muted-foreground">
          Last updated: {format(new Date(project.updated_at), 'PPP')}
        </p>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button asChild size="sm" className="flex-1">
          <Link href={`/projects/${project.project_id}`}>View Project</Link>
        </Button>
        <ExportProjectButton projectId={project.project_id} />
      </CardFooter>
    </DevCard>
  );
}

function ProjectListSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {[...Array(3)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3 mt-1" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-1/4 mb-2" />
            <div className="flex gap-1 mt-1">
              <Skeleton className="h-5 w-12 rounded-full" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
            <Skeleton className="h-3 w-1/2 mt-3" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-9 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

function ProjectsContent() {
  const { data: projectsResponse, isLoading, error, isError } = useQuery({
    queryKey: ['projects'],
    queryFn: getProjects,
  });

  const projects = projectsResponse?.items || [];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="page-header mb-0">Projects</h1>
        <Button asChild>
          <Link href="/projects/create">
            <PlusCircle className="mr-2 h-4 w-4" /> Create Project
          </Link>
        </Button>
      </div>
      <ImportProjectBar />

      {isLoading && <ProjectListSkeleton />}
      {isError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error fetching projects</AlertTitle>
          <AlertDescription>{error?.message || 'An unknown error occurred.'}</AlertDescription>
        </Alert>
      )}
      {!isLoading && !isError && projects.length === 0 && (
        <DevCard className="text-center py-12">
          <CardHeader>
            <FolderKanban className="mx-auto h-12 w-12 text-muted-foreground" />
            <CardTitle className="mt-4">No Projects Yet</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>Get started by creating your first project.</CardDescription>
          </CardContent>
          <CardFooter className="justify-center">
             <Button asChild>
                <Link href="/projects/create">
                  <PlusCircle className="mr-2 h-4 w-4" /> Create Project
                </Link>
              </Button>
          </CardFooter>
        </DevCard>
      )}
      {!isLoading && !isError && projects.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project) => (
            <ProjectCard key={project.project_id} project={project} />
          ))}
        </div>
      )}

    </div>
  );
}

export default function ProjectsPage() {
  return (
    <ProtectedRoute requiredRole="USER">
      <ProjectsContent />
    </ProtectedRoute>
  );
}
