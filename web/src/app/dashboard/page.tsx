'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  FolderKanban, 
  TestTube, 
  Users, 
  Settings, 
  BarChart3,
  Plus,
  ArrowRight,
  Building2,
  Shield
} from 'lucide-react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';

function DashboardContent() {
  const { user } = useAuth();
  const router = useRouter();

  if (!user) return null;

  const quickActions = [
    {
      title: 'Create Project',
      description: 'Start a new testing project',
      icon: FolderKanban,
      href: '/projects/create',
      color: 'bg-blue-500',
    },
    {
      title: 'Run Tests',
      description: 'Execute test suites',
      icon: TestTube,
      href: '/projects',
      color: 'bg-green-500',
    },
    {
      title: 'AI Tools',
      description: 'Generate tests with AI',
      icon: BarChart3,
      href: '/ai-tools',
      color: 'bg-purple-500',
    },
    {
      title: 'Organization',
      description: 'Manage team members',
      icon: Users,
      href: '/organization',
      color: 'bg-orange-500',
    },
  ];

  const stats = [
    {
      title: 'Active Projects',
      value: '0', // This would come from API
      description: 'Projects in your organization',
      icon: FolderKanban,
    },
    {
      title: 'Test Suites',
      value: '0', // This would come from API
      description: 'Total test suites',
      icon: TestTube,
    },
    {
      title: 'Team Members',
      value: '1', // This would come from API
      description: 'Organization members',
      icon: Users,
    },
  ];

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'destructive';
      case 'ORG_ADMIN':
        return 'default';
      case 'USER':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Shield className="h-4 w-4" />;
      case 'ORG_ADMIN':
        return <Users className="h-4 w-4" />;
      case 'USER':
        return <Users className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Welcome Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Welcome back, {user.user.first_name}!
        </h1>
        <div className="flex items-center space-x-4 text-muted-foreground">
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4" />
            <span>{user.organization.name}</span>
          </div>
          <Badge 
            variant={getRoleBadgeVariant(user.user_organization.role)}
            className="flex items-center space-x-1"
          >
            {getRoleIcon(user.user_organization.role)}
            <span>{user.user_organization.role}</span>
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Quick Actions</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action, index) => (
            <Card 
              key={index} 
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push(action.href)}
            >
              <CardHeader className="pb-3">
                <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-2`}>
                  <action.icon className="h-5 w-5 text-white" />
                </div>
                <CardTitle className="text-base">{action.title}</CardTitle>
                <CardDescription className="text-sm">
                  {action.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button variant="ghost" size="sm" className="w-full justify-between p-0">
                  Get Started
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Recent Activity</h2>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8 text-muted-foreground">
              <TestTube className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No recent activity</p>
              <p className="text-sm">
                Start by creating your first project or running some tests
              </p>
              <Button 
                className="mt-4" 
                onClick={() => router.push('/projects/create')}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Project
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Getting Started */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Getting Started</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Set up your first project</CardTitle>
              <CardDescription>
                Create a project to organize your test suites and test cases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => router.push('/projects/create')}
              >
                <FolderKanban className="h-4 w-4 mr-2" />
                Create Project
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Invite team members</CardTitle>
              <CardDescription>
                Collaborate with your team by inviting them to your organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => router.push('/organization')}
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Team
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <ProtectedRoute requiredRole="USER">
      <DashboardContent />
    </ProtectedRoute>
  );
}
