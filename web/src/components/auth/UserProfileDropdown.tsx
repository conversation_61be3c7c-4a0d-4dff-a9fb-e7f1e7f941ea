'use client';

import React, { useState } from 'react';
import { 
  User, 
  Settings, 
  LogOut, 
  Building2, 
  ChevronDown, 
  Shield,
  Users,
  Loader2
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useLogout } from '@/hooks/useAuthQueries';

interface UserProfileDropdownProps {
  className?: string;
}

export function UserProfileDropdown({ className }: UserProfileDropdownProps) {
  const { user, isLoading } = useAuth();
  const logoutMutation = useLogout();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  if (isLoading || !user) {
    return (
      <Button variant="ghost" size="sm" disabled className={className}>
        <Loader2 className="h-4 w-4 animate-spin" />
      </Button>
    );
  }

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getUserInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'destructive';
      case 'ORG_ADMIN':
        return 'default';
      case 'USER':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Shield className="h-3 w-3" />;
      case 'ORG_ADMIN':
        return <Users className="h-3 w-3" />;
      case 'USER':
        return <User className="h-3 w-3" />;
      default:
        return null;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className={`flex items-center space-x-2 h-auto p-2 ${className}`}
        >
          <Avatar className="h-8 w-8">
            <AvatarImage 
              src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.user.first_name}+${user.user.last_name}`}
              alt={`${user.user.first_name} ${user.user.last_name}`}
            />
            <AvatarFallback>
              {getUserInitials(user.user.first_name, user.user.last_name)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex flex-col items-start text-left">
            <span className="text-sm font-medium">
              {user.user.first_name} {user.user.last_name}
            </span>
            <span className="text-xs text-muted-foreground">
              {user.organization.name}
            </span>
          </div>
          
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="pb-2">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2">
              <Avatar className="h-10 w-10">
                <AvatarImage 
                  src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.user.first_name}+${user.user.last_name}`}
                  alt={`${user.user.first_name} ${user.user.last_name}`}
                />
                <AvatarFallback>
                  {getUserInitials(user.user.first_name, user.user.last_name)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex flex-col">
                <span className="font-medium">
                  {user.user.first_name} {user.user.last_name}
                </span>
                <span className="text-xs text-muted-foreground">
                  {user.user.email}
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <Building2 className="h-3 w-3" />
                <span>{user.organization.name}</span>
              </div>
              
              <Badge 
                variant={getRoleBadgeVariant(user.user_organization.role)}
                className="text-xs"
              >
                <span className="flex items-center space-x-1">
                  {getRoleIcon(user.user_organization.role)}
                  <span>{user.user_organization.role}</span>
                </span>
              </Badge>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <a href="/profile" className="flex items-center space-x-2 cursor-pointer">
            <User className="h-4 w-4" />
            <span>Profile Settings</span>
          </a>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <a href="/organization" className="flex items-center space-x-2 cursor-pointer">
            <Building2 className="h-4 w-4" />
            <span>Organization</span>
          </a>
        </DropdownMenuItem>

        {(user.user_organization.role === 'ORG_ADMIN' || user.user_organization.role === 'ADMIN') && (
          <DropdownMenuItem asChild>
            <a href="/organization/settings" className="flex items-center space-x-2 cursor-pointer">
              <Settings className="h-4 w-4" />
              <span>Organization Settings</span>
            </a>
          </DropdownMenuItem>
        )}

        {user.user_organization.role === 'ADMIN' && (
          <DropdownMenuItem asChild>
            <a href="/admin" className="flex items-center space-x-2 cursor-pointer">
              <Shield className="h-4 w-4" />
              <span>Admin Panel</span>
            </a>
          </DropdownMenuItem>
        )}

        <DropdownMenuSeparator />

        <DropdownMenuItem 
          onClick={handleLogout}
          disabled={isLoggingOut}
          className="text-red-600 focus:text-red-600 focus:bg-red-50"
        >
          <div className="flex items-center space-x-2">
            {isLoggingOut ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <LogOut className="h-4 w-4" />
            )}
            <span>{isLoggingOut ? 'Signing out...' : 'Sign out'}</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact version for mobile or smaller spaces
export function UserProfileDropdownCompact({ className }: UserProfileDropdownProps) {
  const { user, isLoading } = useAuth();
  const logoutMutation = useLogout();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  if (isLoading || !user) {
    return (
      <Button variant="ghost" size="sm" disabled className={className}>
        <Loader2 className="h-4 w-4 animate-spin" />
      </Button>
    );
  }

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getUserInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className={`p-1 ${className}`}>
          <Avatar className="h-8 w-8">
            <AvatarImage 
              src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.user.first_name}+${user.user.last_name}`}
              alt={`${user.user.first_name} ${user.user.last_name}`}
            />
            <AvatarFallback>
              {getUserInitials(user.user.first_name, user.user.last_name)}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <span className="font-medium">
              {user.user.first_name} {user.user.last_name}
            </span>
            <span className="text-xs text-muted-foreground">
              {user.user.email}
            </span>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <a href="/profile" className="flex items-center space-x-2 cursor-pointer">
            <User className="h-4 w-4" />
            <span>Profile</span>
          </a>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <a href="/organization" className="flex items-center space-x-2 cursor-pointer">
            <Building2 className="h-4 w-4" />
            <span>Organization</span>
          </a>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem 
          onClick={handleLogout}
          disabled={isLoggingOut}
          className="text-red-600 focus:text-red-600 focus:bg-red-50"
        >
          <div className="flex items-center space-x-2">
            {isLoggingOut ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <LogOut className="h-4 w-4" />
            )}
            <span>{isLoggingOut ? 'Signing out...' : 'Sign out'}</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default UserProfileDropdown;
