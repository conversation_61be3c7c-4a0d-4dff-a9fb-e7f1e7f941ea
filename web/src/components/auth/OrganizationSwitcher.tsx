'use client';

import React, { useState } from 'react';
import { 
  Building2, 
  ChevronDown, 
  Check, 
  Plus,
  Loader2,
  Users,
  Settings
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { useUserOrganizations, useSwitchOrganization } from '@/hooks/useAuthQueries';

interface OrganizationSwitcherProps {
  className?: string;
  showLabel?: boolean;
}

export function OrganizationSwitcher({ 
  className, 
  showLabel = true 
}: OrganizationSwitcherProps) {
  const { user, isLoading: authLoading } = useAuth();
  const { data: organizationsData, isLoading: orgsLoading } = useUserOrganizations();
  const switchOrgMutation = useSwitchOrganization();
  const [isSwitching, setIsSwitching] = useState(false);

  if (authLoading || !user) {
    return (
      <Button variant="ghost" size="sm" disabled className={className}>
        <Loader2 className="h-4 w-4 animate-spin" />
        {showLabel && <span className="ml-2">Loading...</span>}
      </Button>
    );
  }

  const currentOrganization = user.organization;
  const organizations = organizationsData?.organizations || [];

  const handleSwitchOrganization = async (organizationId: string) => {
    if (organizationId === currentOrganization.organization_id || isSwitching) {
      return;
    }

    try {
      setIsSwitching(true);
      await switchOrgMutation.mutateAsync(organizationId);
      // The mutation will handle updating the context and reloading
    } catch (error) {
      console.error('Failed to switch organization:', error);
    } finally {
      setIsSwitching(false);
    }
  };

  const getOrganizationInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'destructive';
      case 'ORG_ADMIN':
        return 'default';
      case 'USER':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className={`flex items-center space-x-2 h-auto p-2 ${className}`}
          disabled={isSwitching}
        >
          <Avatar className="h-6 w-6">
            <AvatarFallback className="text-xs">
              {getOrganizationInitials(currentOrganization.name)}
            </AvatarFallback>
          </Avatar>
          
          {showLabel && (
            <div className="flex flex-col items-start text-left">
              <span className="text-sm font-medium truncate max-w-32">
                {currentOrganization.name}
              </span>
              <span className="text-xs text-muted-foreground">
                {user.user_organization.role}
              </span>
            </div>
          )}
          
          {isSwitching ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-64">
        <DropdownMenuLabel>
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4" />
            <span>Switch Organization</span>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {orgsLoading ? (
          <DropdownMenuItem disabled>
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading organizations...</span>
            </div>
          </DropdownMenuItem>
        ) : (
          <>
            {organizations.map(({ organization, user_organization }) => {
              const isCurrentOrg = organization.organization_id === currentOrganization.organization_id;
              
              return (
                <DropdownMenuItem
                  key={organization.organization_id}
                  onClick={() => handleSwitchOrganization(organization.organization_id)}
                  disabled={isCurrentOrg || isSwitching}
                  className="flex items-center justify-between p-3 cursor-pointer"
                >
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getOrganizationInitials(organization.name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">
                        {organization.name}
                      </span>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant={getRoleBadgeVariant(user_organization.role)}
                          className="text-xs"
                        >
                          {user_organization.role}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  {isCurrentOrg && (
                    <Check className="h-4 w-4 text-green-600" />
                  )}
                </DropdownMenuItem>
              );
            })}

            {organizations.length === 0 && (
              <DropdownMenuItem disabled>
                <span className="text-muted-foreground">No organizations found</span>
              </DropdownMenuItem>
            )}
          </>
        )}

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <a href="/organization/join" className="flex items-center space-x-2 cursor-pointer">
            <Plus className="h-4 w-4" />
            <span>Join Organization</span>
          </a>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <a href="/organization/create" className="flex items-center space-x-2 cursor-pointer">
            <Building2 className="h-4 w-4" />
            <span>Create Organization</span>
          </a>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <a href="/organization" className="flex items-center space-x-2 cursor-pointer">
            <Users className="h-4 w-4" />
            <span>Manage Members</span>
          </a>
        </DropdownMenuItem>

        {(user.user_organization.role === 'ORG_ADMIN' || user.user_organization.role === 'ADMIN') && (
          <DropdownMenuItem asChild>
            <a href="/organization/settings" className="flex items-center space-x-2 cursor-pointer">
              <Settings className="h-4 w-4" />
              <span>Organization Settings</span>
            </a>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact version for mobile or smaller spaces
export function OrganizationSwitcherCompact({ className }: OrganizationSwitcherProps) {
  const { user, isLoading: authLoading } = useAuth();
  const { data: organizationsData, isLoading: orgsLoading } = useUserOrganizations();
  const switchOrgMutation = useSwitchOrganization();
  const [isSwitching, setIsSwitching] = useState(false);

  if (authLoading || !user) {
    return (
      <Button variant="ghost" size="sm" disabled className={className}>
        <Loader2 className="h-4 w-4 animate-spin" />
      </Button>
    );
  }

  const currentOrganization = user.organization;
  const organizations = organizationsData?.organizations || [];

  const handleSwitchOrganization = async (organizationId: string) => {
    if (organizationId === currentOrganization.organization_id || isSwitching) {
      return;
    }

    try {
      setIsSwitching(true);
      await switchOrgMutation.mutateAsync(organizationId);
    } catch (error) {
      console.error('Failed to switch organization:', error);
    } finally {
      setIsSwitching(false);
    }
  };

  const getOrganizationInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className={`p-1 ${className}`}
          disabled={isSwitching}
        >
          {isSwitching ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {getOrganizationInitials(currentOrganization.name)}
              </AvatarFallback>
            </Avatar>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-48">
        <DropdownMenuLabel>Organizations</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {orgsLoading ? (
          <DropdownMenuItem disabled>
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span>Loading...</span>
          </DropdownMenuItem>
        ) : (
          <>
            {organizations.map(({ organization }) => {
              const isCurrentOrg = organization.organization_id === currentOrganization.organization_id;
              
              return (
                <DropdownMenuItem
                  key={organization.organization_id}
                  onClick={() => handleSwitchOrganization(organization.organization_id)}
                  disabled={isCurrentOrg || isSwitching}
                  className="flex items-center justify-between"
                >
                  <span className="truncate">{organization.name}</span>
                  {isCurrentOrg && <Check className="h-4 w-4 text-green-600" />}
                </DropdownMenuItem>
              );
            })}
          </>
        )}

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <a href="/organization" className="cursor-pointer">
            <Building2 className="h-4 w-4 mr-2" />
            <span>Manage</span>
          </a>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default OrganizationSwitcher;
