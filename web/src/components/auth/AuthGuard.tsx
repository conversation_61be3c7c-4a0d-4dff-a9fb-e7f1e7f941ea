'use client';

import React, { ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
];

export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const pathname = usePathname();

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES.some(route => pathname.startsWith(route));

  // Show loading state
  if (isLoading) {
    return <LoadingScreen />;
  }

  // For public routes (auth pages), show ONLY the auth content without sidebar
  if (isPublicRoute) {
    return <PublicLayout>{children}</PublicLayout>;
  }

  // For authenticated users, show full app with sidebar
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // For unauthenticated users on any other route, redirect to login
  if (typeof window !== 'undefined') {
    window.location.href = '/auth/login';
  }
  return <LoadingScreen />;
}

// Loading screen component
function LoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">Loading QAK...</p>
      </div>
    </div>
  );
}

// Public layout for auth pages (no sidebar, minimal header)
function PublicLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  );
}



export default AuthGuard;
