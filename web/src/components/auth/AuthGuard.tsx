'use client';

import React, { ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
];

// Routes that should show minimal UI for unauthenticated users
const LANDING_ROUTES = [
  '/',
];

export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const pathname = usePathname();

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES.some(route => pathname.startsWith(route));
  const isLandingRoute = LANDING_ROUTES.includes(pathname);

  // Show loading state
  if (isLoading) {
    return <LoadingScreen />;
  }

  // For public routes (auth pages), always show content
  if (isPublicRoute) {
    return <PublicLayout>{children}</PublicLayout>;
  }

  // For authenticated users, show full app
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // For unauthenticated users on landing page, show minimal UI
  if (isLandingRoute) {
    return <LandingLayout>{children}</LandingLayout>;
  }

  // For all other routes, redirect to login
  // This will be handled by individual ProtectedRoute components
  return <>{children}</>;
}

// Loading screen component
function LoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">Loading QAK...</p>
      </div>
    </div>
  );
}

// Public layout for auth pages (no sidebar, minimal header)
function PublicLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  );
}

// Landing layout for unauthenticated users on home page
function LandingLayout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen bg-background">
      <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background/80 backdrop-blur-sm px-4 md:px-6">
        <div className="flex-1">
          <h1 className="text-xl font-bold">QAK</h1>
        </div>
        <div className="flex items-center gap-2">
          <a 
            href="/auth/login"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
          >
            Sign in
          </a>
          <a 
            href="/auth/register"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2"
          >
            Sign up
          </a>
        </div>
      </header>
      <main className="container mx-auto py-8">
        <div className="text-center space-y-6">
          <h1 className="text-4xl font-bold tracking-tight">
            Welcome to QAK
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Quality Assurance Kit - Your comprehensive test automation and quality assurance platform
          </p>
          <div className="flex justify-center gap-4">
            <a 
              href="/auth/register"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-8"
            >
              Get Started
            </a>
            <a 
              href="/auth/login"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-8"
            >
              Sign In
            </a>
          </div>
        </div>
      </main>
    </div>
  );
}

export default AuthGuard;
