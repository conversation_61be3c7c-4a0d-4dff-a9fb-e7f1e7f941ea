'use client';

import React, { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, Shield, AlertTriangle } from 'lucide-react';

import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  'USER': 1,
  'ORG_ADMIN': 2,
  'ADMIN': 3,
};

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: 'USER' | 'ORG_ADMIN' | 'ADMIN';
  requiredPermissions?: string[];
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

export function ProtectedRoute({
  children,
  requiredRole,
  requiredPermissions = [],
  fallbackPath = '/auth/login',
  showUnauthorized = true,
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(fallbackPath);
    }
  }, [isLoading, isAuthenticated, router, fallbackPath]);

  // Show loading state
  if (isLoading) {
    return <LoadingScreen />;
  }

  // Redirect if not authenticated
  if (!isAuthenticated || !user) {
    return null; // Will redirect via useEffect
  }

  // Check role requirements
  if (requiredRole) {
    const userRoleLevel = ROLE_HIERARCHY[user.user_organization.role];
    const requiredRoleLevel = ROLE_HIERARCHY[requiredRole];

    if (userRoleLevel < requiredRoleLevel) {
      if (showUnauthorized) {
        return <UnauthorizedScreen requiredRole={requiredRole} userRole={user.user_organization.role} />;
      }
      router.push('/dashboard');
      return null;
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission =>
      user.permissions.includes(permission) ||
      user.permissions.includes('*:*') ||
      (permission.includes(':') && user.permissions.includes(`*:${permission.split(':')[1]}`))
    );

    if (!hasAllPermissions) {
      if (showUnauthorized) {
        return <UnauthorizedScreen requiredPermissions={requiredPermissions} userPermissions={user.permissions} />;
      }
      router.push('/dashboard');
      return null;
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Loading screen component
function LoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Loading...
          </h2>
          <p className="text-sm text-gray-600 text-center">
            Please wait while we verify your authentication
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

// Unauthorized screen component
interface UnauthorizedScreenProps {
  requiredRole?: string;
  userRole?: string;
  requiredPermissions?: string[];
  userPermissions?: string[];
}

function UnauthorizedScreen({
  requiredRole,
  userRole,
  requiredPermissions,
  userPermissions,
}: UnauthorizedScreenProps) {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <Shield className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl font-semibold text-gray-900">
            Access Denied
          </CardTitle>
          <CardDescription>
            You don't have permission to access this page
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {requiredRole && (
                <div>
                  <strong>Required role:</strong> {requiredRole}
                  <br />
                  <strong>Your role:</strong> {userRole}
                </div>
              )}
              {requiredPermissions && requiredPermissions.length > 0 && (
                <div>
                  <strong>Required permissions:</strong>
                  <ul className="list-disc list-inside mt-1">
                    {requiredPermissions.map((permission, index) => (
                      <li key={index} className="text-sm">{permission}</li>
                    ))}
                  </ul>
                </div>
              )}
            </AlertDescription>
          </Alert>

          <div className="flex flex-col space-y-2">
            <Button
              onClick={() => router.push('/dashboard')}
              className="w-full"
            >
              Go to Dashboard
            </Button>
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="w-full"
            >
              Go Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Higher-order component for role-based protection
export function withRoleProtection<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole: 'USER' | 'ORG_ADMIN' | 'ADMIN'
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute requiredRole={requiredRole}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Higher-order component for permission-based protection
export function withPermissionProtection<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions: string[]
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute requiredPermissions={requiredPermissions}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Hook for checking permissions in components
export function usePermissions() {
  const { user } = useAuth();

  const hasRole = (role: 'USER' | 'ORG_ADMIN' | 'ADMIN'): boolean => {
    if (!user) return false;
    
    const userRoleLevel = ROLE_HIERARCHY[user.user_organization.role];
    const requiredRoleLevel = ROLE_HIERARCHY[role];
    
    return userRoleLevel >= requiredRoleLevel;
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    return (
      user.permissions.includes(permission) ||
      user.permissions.includes('*:*') ||
      (permission.includes(':') && user.permissions.includes(`*:${permission.split(':')[1]}`))
    );
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  return {
    hasRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userRole: user?.user_organization.role,
    userPermissions: user?.permissions || [],
  };
}

export default ProtectedRoute;
