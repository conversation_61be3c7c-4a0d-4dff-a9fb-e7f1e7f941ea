# System Patterns: AgentQA - Automatización de Pruebas con IA

## System Architecture

AgentQA implementa una arquitectura modular multicapa que separa responsabilidades y facilita el mantenimiento y escalabilidad.

```mermaid
graph TD
    subgraph "Frontend Layer"
        WEB[Next.js Web Interface]
        CLI[Python CLI Interface]
        API_CLIENT[API Client Libraries]
    end

    subgraph "API Layer"
        FASTAPI[FastAPI Application]
        PROJECT_ROUTES[Project Routes]
        SUITE_ROUTES[Suite Routes]
        TESTCASE_ROUTES[TestCase Routes]
        PROMPT_ROUTES[Prompt Routes]
        CONFIG_ROUTES[Config Routes]
        TRANSLATION_ROUTES[Translation Routes]
    end

    subgraph "Service Layer"
        TEST_SERVICE[TestService Core]
        PROMPT_SERVICE[PromptService]
        STORY_AGENT[StoryAgent]
        BROWSER_AGENT[BrowserAutomationAgent]
        PROJECT_MANAGER[ProjectManagerService]
    end

    subgraph "Integration Layer"
        BROWSER_USE[browser-use Library]
        LANGCHAIN[LangChain Framework]
        AI_PROVIDERS[AI Providers: Gemini/OpenAI/Claude/Groq]
    end

    subgraph "Authentication Layer (NEW - Phase 4)"
        AUTH_SERVICE[Authentication Service]
        JWT_MIDDLEWARE[JWT Middleware]
        RBAC_SERVICE[Role-Based Access Control]
        ORG_CONTEXT[Organization Context]
    end

    subgraph "Storage Layer"
        PROJECT_FILES[JSON Project Files]
        PROMPT_TEMPLATES[Markdown Prompt Templates]
        TEST_HISTORY[Test Execution History]
        SCREENSHOTS[Screenshot Storage]
        MONGODB[MongoDB (Auth Models)]
    end

    WEB --> FASTAPI
    CLI --> TEST_SERVICE
    API_CLIENT --> FASTAPI
    
    FASTAPI --> PROJECT_ROUTES
    FASTAPI --> SUITE_ROUTES
    FASTAPI --> TESTCASE_ROUTES
    FASTAPI --> PROMPT_ROUTES
    FASTAPI --> CONFIG_ROUTES
    FASTAPI --> TRANSLATION_ROUTES
    
    PROJECT_ROUTES --> TEST_SERVICE
    SUITE_ROUTES --> TEST_SERVICE
    TESTCASE_ROUTES --> TEST_SERVICE
    PROMPT_ROUTES --> PROMPT_SERVICE
    
    TEST_SERVICE --> STORY_AGENT
    TEST_SERVICE --> BROWSER_AGENT
    TEST_SERVICE --> PROJECT_MANAGER
    
    STORY_AGENT --> LANGCHAIN
    BROWSER_AGENT --> BROWSER_USE
    BROWSER_USE --> AI_PROVIDERS
    LANGCHAIN --> AI_PROVIDERS
    
    PROJECT_MANAGER --> PROJECT_FILES
    BROWSER_AGENT --> TEST_HISTORY
    BROWSER_AGENT --> SCREENSHOTS
    PROMPT_SERVICE --> PROMPT_TEMPLATES
    
    WEB --> JWT_MIDDLEWARE
    API_CLIENT --> JWT_MIDDLEWARE
    JWT_MIDDLEWARE --> AUTH_SERVICE
    JWT_MIDDLEWARE --> RBAC_SERVICE
    AUTH_SERVICE --> MONGODB
    RBAC_SERVICE --> ORG_CONTEXT
    ORG_CONTEXT --> PROJECT_MANAGER
    ORG_CONTEXT --> TEST_SERVICE

    style FASTAPI fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style TEST_SERVICE fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style BROWSER_USE fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style AI_PROVIDERS fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style AUTH_SERVICE fill:#ffebee,stroke:#c62828,stroke-width:2px
    style JWT_MIDDLEWARE fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
```

## Authentication & Authorization Architectural Patterns (Phase 3 Complete)

### **1. JWT-Based Stateless Authentication Pattern** 

**Pattern**: Stateless token-based authentication using JSON Web Tokens for scalable, secure user authentication

```python
# JWT Authentication Service Pattern
class JWTAuthService:
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_expire = timedelta(minutes=15)
        self.refresh_token_expire = timedelta(days=7)
    
    async def create_access_token(self, user_id: ObjectId, organization_id: ObjectId, role: str) -> str:
        payload = {
            "sub": str(user_id),
            "org_id": str(organization_id),
            "role": role,
            "exp": datetime.utcnow() + self.access_token_expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    async def validate_token(self, token: str) -> TokenPayload:
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return TokenPayload(**payload)
        except JWTError:
            raise InvalidTokenException()
```

**Benefits**: Fast validation (no DB lookup), horizontal scaling, organization context embedded, industry standard

**Implementation**: FastAPI Security + python-jose + MongoDB token blacklisting

### **2. Multi-Tenant Repository Pattern**

**Pattern**: Automatic organization scoping for all database operations ensuring complete data isolation

```python
# Multi-Tenant Repository Base Pattern
class MultiTenantRepository:
    def __init__(self, model_class: Type[Document]):
        self.model_class = model_class
    
    async def find_all(self, organization_id: ObjectId, filters: dict = None) -> List[Document]:
        query = {"organization_id": organization_id}
        if filters:
            query.update(filters)
        return await self.model_class.find(query).to_list()
    
    async def find_by_id(self, organization_id: ObjectId, entity_id: ObjectId) -> Document:
        entity = await self.model_class.find_one({
            "_id": entity_id,
            "organization_id": organization_id
        })
        if not entity:
            raise EntityNotFoundError(f"Entity {entity_id} not found in organization {organization_id}")
        return entity
    
    async def create(self, organization_id: ObjectId, data: dict) -> Document:
        data["organization_id"] = organization_id
        entity = self.model_class(**data)
        await entity.insert()
        return entity
```

**Benefits**: Automatic data isolation, zero cross-tenant access, type-safe operations, consistent patterns

**Implementation**: Beanie ODM with organization_id indexing and automatic scoping

### **3. Role-Based Access Control (RBAC) Pattern**

**Pattern**: Hierarchical role system with organization-scoped permissions and middleware validation

```python
# RBAC Service Pattern
class RBACService:
    class Role(str, Enum):
        USER = "user"           # Project-level access
        ORG_ADMIN = "org_admin" # Organization management
        ADMIN = "admin"         # System administration
    
    ROLE_PERMISSIONS = {
        Role.USER: ["read:projects", "write:projects", "read:test_cases", "write:test_cases"],
        Role.ORG_ADMIN: ["*:organization", "read:users", "write:users", "admin:organization"],
        Role.ADMIN: ["*:*"]  # All permissions
    }
    
    async def check_permission(self, user_role: Role, required_permission: str) -> bool:
        user_permissions = self.ROLE_PERMISSIONS.get(user_role, [])
        
        # Check for wildcard permissions
        if "*:*" in user_permissions:
            return True
            
        # Check for resource wildcard
        resource = required_permission.split(":")[1]
        if f"*:{resource}" in user_permissions:
            return True
            
        # Check for exact permission
        return required_permission in user_permissions
```

**Benefits**: Clear permission hierarchy, JWT-embedded roles, fast validation, extensible design

**Implementation**: FastAPI dependencies with role validation middleware

### **4. Authentication Middleware Chain Pattern**

**Pattern**: Layered middleware stack for comprehensive request authentication and authorization

```python
# Authentication Middleware Chain
class AuthenticationMiddleware:
    async def __call__(self, request: Request, call_next):
        # Skip authentication for public endpoints
        if self._is_public_endpoint(request.url.path):
            return await call_next(request)
        
        # Extract and validate JWT token
        token = self._extract_token(request)
        if not token:
            raise HTTPException(401, "Missing authentication token")
        
        try:
            payload = await self.jwt_service.validate_token(token)
            request.state.user_id = payload.sub
            request.state.organization_id = payload.org_id
            request.state.user_role = payload.role
        except InvalidTokenException:
            raise HTTPException(401, "Invalid authentication token")
        
        # Check token blacklist
        if await self.token_blacklist.is_blacklisted(token):
            raise HTTPException(401, "Token has been revoked")
        
        return await call_next(request)

class OrganizationMiddleware:
    async def __call__(self, request: Request, call_next):
        if hasattr(request.state, 'organization_id'):
            # Inject organization context into all downstream services
            request.state.organization = await self.org_service.get_organization(
                request.state.organization_id
            )
        return await call_next(request)

class RBACMiddleware:
    async def __call__(self, request: Request, call_next):
        if hasattr(request.state, 'user_role'):
            # Validate user permissions for requested resource
            required_permission = self._determine_required_permission(request)
            if not await self.rbac_service.check_permission(
                request.state.user_role, required_permission
            ):
                raise HTTPException(403, "Insufficient permissions")
        return await call_next(request)
```

**Benefits**: Separation of concerns, reusable components, comprehensive security, audit trail

**Implementation**: FastAPI middleware with dependency injection

### **5. Organization Context Pattern**

**Pattern**: Automatic injection of organization context into all business operations

```python
# Organization Context Service Pattern
class OrganizationContext:
    def __init__(self, request: Request):
        self.request = request
    
    @property
    def organization_id(self) -> ObjectId:
        if not hasattr(self.request.state, 'organization_id'):
            raise UnauthorizedError("No organization context available")
        return ObjectId(self.request.state.organization_id)
    
    @property 
    def user_id(self) -> ObjectId:
        if not hasattr(self.request.state, 'user_id'):
            raise UnauthorizedError("No user context available")
        return ObjectId(self.request.state.user_id)
    
    @property
    def user_role(self) -> str:
        return getattr(self.request.state, 'user_role', 'user')
    
    async def scope_query(self, base_query: dict) -> dict:
        """Automatically add organization scoping to any query"""
        scoped_query = {"organization_id": self.organization_id}
        scoped_query.update(base_query)
        return scoped_query

# FastAPI Dependency for Organization Context
async def get_organization_context(request: Request) -> OrganizationContext:
    return OrganizationContext(request)

# Usage in API endpoints
@router.get("/projects")
async def get_projects(
    org_context: OrganizationContext = Depends(get_organization_context)
):
    query = await org_context.scope_query({})
    projects = await Project.find(query).to_list()
    return projects
```

**Benefits**: Automatic scoping, consistent context, type safety, dependency injection

**Implementation**: FastAPI dependencies with automatic organization injection

### **6. Token Lifecycle Management Pattern**

**Pattern**: Comprehensive token management with rotation, blacklisting, and secure refresh

```python
# Token Lifecycle Management Pattern
class TokenManager:
    def __init__(self, jwt_service: JWTService, blacklist_service: TokenBlacklistService):
        self.jwt_service = jwt_service
        self.blacklist_service = blacklist_service
    
    async def login(self, email: str, password: str) -> TokenPair:
        # Authenticate user
        user = await self.auth_service.authenticate(email, password)
        
        # Create token pair
        access_token = await self.jwt_service.create_access_token(
            user.id, user.primary_organization_id, user.role
        )
        refresh_token = await self.jwt_service.create_refresh_token(user.id)
        
        # Store refresh token hash for validation
        await self.refresh_token_store.save(refresh_token, user.id)
        
        return TokenPair(access_token=access_token, refresh_token=refresh_token)
    
    async def refresh_tokens(self, refresh_token: str) -> TokenPair:
        # Validate refresh token
        payload = await self.jwt_service.validate_refresh_token(refresh_token)
        user = await self.user_service.get_user(payload.sub)
        
        # Revoke old refresh token (rotation)
        await self.blacklist_service.blacklist_token(refresh_token)
        
        # Create new token pair
        return await self.create_token_pair(user)
    
    async def logout(self, access_token: str, refresh_token: str):
        # Blacklist both tokens
        await self.blacklist_service.blacklist_token(access_token)
        await self.blacklist_service.blacklist_token(refresh_token)
        
        # Log security event
        await self.audit_service.log_logout(access_token)
```

**Benefits**: Secure token rotation, immediate revocation, audit trail, session management

**Implementation**: MongoDB TTL collections for blacklisting, structured audit logging

### **7. Security Audit Pattern**

**Pattern**: Comprehensive security event logging and monitoring for compliance and threat detection

```python
# Security Audit Pattern
class SecurityAuditService:
    async def log_authentication_event(
        self, 
        user_id: ObjectId, 
        event_type: str,
        success: bool,
        ip_address: str,
        user_agent: str,
        additional_context: dict = None
    ):
        audit_event = {
            "timestamp": datetime.utcnow(),
            "event_type": event_type,
            "user_id": str(user_id),
            "success": success,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "context": additional_context or {},
            "correlation_id": str(uuid.uuid4())
        }
        
        await self.audit_collection.insert_one(audit_event)
        
        # Trigger alerts for suspicious activity
        if not success:
            await self.security_monitor.check_failed_attempts(user_id, ip_address)
    
    async def log_authorization_event(
        self,
        user_id: ObjectId,
        organization_id: ObjectId,
        resource: str,
        action: str,
        allowed: bool,
        reason: str = None
    ):
        await self.audit_collection.insert_one({
            "timestamp": datetime.utcnow(),
            "event_type": "authorization",
            "user_id": str(user_id),
            "organization_id": str(organization_id),
            "resource": resource,
            "action": action,
            "allowed": allowed,
            "reason": reason,
            "correlation_id": str(uuid.uuid4())
        })
```

**Benefits**: Complete audit trail, threat detection, compliance support, correlation tracking

**Implementation**: Structured logging with MongoDB storage and monitoring integration

### **Updated System Architecture with Authentication**
