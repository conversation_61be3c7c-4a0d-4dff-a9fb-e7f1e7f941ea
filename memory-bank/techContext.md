# Tech Context: AgentQA - Automatización de Pruebas con IA

## Technologies Used

### **Backend (Python)**
*   **Core Framework:** FastAPI - API REST moderna y asíncrona
*   **Web Server:** Uvicorn - Servidor ASGI de alto rendimiento
*   **Browser Automation:** browser-use==0.2.5 - Automatización web con IA
*   **AI/LLM Frameworks:**
    *   LangChain - Framework para aplicaciones con LLM
    *   langchain-google-genai - Integración con Gemini
    *   langchain-openai - Integración con OpenAI
    *   langchain-anthropic - Integración con Claude
    *   langchain-groq - Integración con Groq
    *   agno - Framework adicional de IA

### **Frontend (TypeScript/React)**
*   **Framework:** Next.js 15+ con App Router
*   **UI Framework:** React 18+ con TypeScript
*   **Styling:** Tailwind CSS + shadcn/ui components
*   **State Management:** TanStack Query (React Query)
*   **Form Management:** React Hook Form + Zod validation
*   **UI Components:**
    *   Radix UI primitives (@radix-ui/react-*)
    *   Lucide React icons
    *   Custom shadcn/ui components
*   **Features Implemented:**
    *   Complete dashboard with sidebar navigation
    *   Project management interface
    *   QA Assistant with AI-powered tools
    *   Smoke Test Playground
    *   Browser Configuration Manager
    *   Prompts management with ES↔EN translation

### **Data Processing & Utilities**
*   **Data Science:** NumPy, Pandas
*   **CLI Interface:** Custom CLI con Python
*   **Formatting:** Tabulate para salida de datos

### **Development Tools**
*   **Environment:** Python-dotenv para configuración
*   **API Documentation:** FastAPI auto-genera Swagger/OpenAPI
*   **Type Safety:** Pydantic para validación de datos
*   **Package Management:** npm/yarn para frontend, pip para backend

### **Authentication & Authorization Stack (NEW - Phase 4)**
*   **Architecture Decision (ADR-001):** JWT-Based Stateless Authentication chosen for performance and scalability
*   **Authentication Framework:** FastAPI Security with OAuth2 + JWT (python-jose library)
*   **Password Hashing:** passlib with bcrypt algorithm (12 salt rounds minimum)
*   **JWT Configuration:** HS256 algorithm, 15-minute access tokens, 7-day refresh tokens
*   **Token Management:** MongoDB TTL collections for token blacklisting and revocation
*   **Multi-Tenancy (ADR-002):** Organization-based data isolation with automatic organization_id scoping
*   **Role-Based Access (ADR-003):** Three-tier system (USER, ORG_ADMIN, ADMIN) with JWT-embedded roles
*   **Middleware Stack (ADR-006):** 
    *   AuthenticationMiddleware: JWT token validation on all protected routes
    *   OrganizationMiddleware: Organization context injection from JWT claims
    *   RBACMiddleware: Role-based permission validation
    *   RateLimitingMiddleware: Brute force protection (5 attempts/5min)
    *   AuditMiddleware: Comprehensive security event logging
*   **Database Models:** MongoDB with Beanie ODM for User, Organization, UserOrganization entities
*   **Environment Isolation (ADR-005):** Leverage existing MONGODB_ENVIRONMENT system for qak_dev database
*   **Frontend Auth:** Next.js with TanStack Query for auth state, secure token storage patterns
*   **Security Implementation (ADR-006):** 
    *   HTTPS enforcement for all authentication endpoints
    *   httpOnly + secure + sameSite cookie settings for refresh tokens
    *   CORS configuration for authenticated cross-origin requests
    *   Comprehensive security headers (HSTS, CSP, X-Frame-Options)
    *   Rate limiting and IP-based brute force protection
    *   Structured audit logging with correlation IDs for security monitoring

## **Additional Dependencies for Authentication (Phase 4)**
```bash
# Core Authentication Dependencies
pip install passlib[bcrypt]>=1.7.4          # Secure password hashing
pip install python-jose[cryptography]>=3.3.0 # JWT token generation/validation
pip install python-multipart>=0.0.6         # Form data support for login

# Security and Validation Dependencies  
pip install email-validator>=2.0.0          # Email format validation
pip install pydantic[email]>=2.0.0          # Enhanced email validation with Pydantic

# Optional: Enhanced Security Dependencies
pip install slowapi>=0.1.9                  # Rate limiting middleware
pip install python-dateutil>=2.8.2          # Enhanced datetime handling for tokens
```

## **JWT Configuration (Architectural Decisions)**
```bash
# JWT Security Configuration (from ADR-001 & ADR-006)
JWT_SECRET_KEY=your_256_bit_secret_key_here          # 256-bit cryptographically secure key
JWT_ALGORITHM=HS256                                  # Industry standard, fast validation
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15                   # Short-lived for security
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7                      # Balanced security/usability

# Password Security Configuration (from ADR-006)
PASSWORD_HASH_ROUNDS=12                              # bcrypt salt rounds (industry standard)
PASSWORD_MIN_LENGTH=8                                # Minimum password complexity
PASSWORD_REQUIRE_SPECIAL_CHARS=true                  # Enhanced password policy

# Rate Limiting Configuration (from ADR-006)
MAX_LOGIN_ATTEMPTS=5                                 # Brute force protection
RATE_LIMIT_WINDOW_MINUTES=5                          # Rate limiting window
LOGIN_RATE_LIMIT_PER_MINUTE=10                       # Login requests per minute per IP

# Multi-Tenancy Configuration (from ADR-002 & ADR-005)
MONGODB_ENVIRONMENT=development                       # Database isolation (qak_dev)
DEFAULT_ORGANIZATION_NAME="Default Organization"     # Migration default org
ENABLE_MULTI_ORG_USERS=true                         # Users can belong to multiple orgs

# Security Headers Configuration (from ADR-006)
ENABLE_SECURITY_HEADERS=true                         # Comprehensive security headers
CORS_ALLOWED_ORIGINS=["http://localhost:9002"]       # Development CORS settings
SECURE_COOKIES=true                                  # Secure cookie settings (production)
```

## **Database Schema Evolution (Multi-Tenant Architecture)**
```python
# Core Authentication Models (from ADR-002 & ADR-003)
from beanie import Document, Indexed
from pydantic import EmailStr, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class Role(str, Enum):
    USER = "user"           # Standard organization member
    ORG_ADMIN = "org_admin" # Organization administrator  
    ADMIN = "admin"         # System administrator

class User(Document):
    email: Indexed(EmailStr, unique=True)
    password_hash: str
    first_name: str
    last_name: str
    is_active: bool = True
    email_verified: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None

class Organization(Document):
    name: Indexed(str, unique=True)
    slug: Indexed(str, unique=True)  # URL-friendly identifier
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True
    settings: dict = Field(default_factory=dict)

class UserOrganization(Document):
    user_id: ObjectId
    organization_id: ObjectId
    role: Role = Role.USER
    joined_at: datetime = Field(default_factory=datetime.utcnow)
    is_active: bool = True
    
    class Settings:
        indexes = [
            [("user_id", 1), ("organization_id", 1)],  # Compound index
            "organization_id",  # Organization queries
            "user_id"          # User queries
        ]

# Multi-Tenant Business Models (organization_id scoping)
class Project(Document):
    organization_id: Indexed(ObjectId)  # Multi-tenant scoping
    name: str
    metadata: dict = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Settings:
        indexes = [
            [("organization_id", 1), ("name", 1)],  # Org-scoped unique names
            "organization_id"  # Fast organization queries
        ]
```

## Development Setup

### **Backend Requirements**
```bash
# Configurar entorno virtual (recomendado)
python3.11 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Instalar dependencias Python
pip install -r requirements.txt

# Dependencias adicionales para autenticación (NEW - Phase 4)
pip install passlib[bcrypt]      # Password hashing
pip install python-jose[cryptography]  # JWT tokens
pip install python-multipart    # Form data support

# Instalar navegadores de Playwright
playwright install

# Variables de entorno requeridas
GOOGLE_API_KEY=your_gemini_api_key  # Principal
OPENAI_API_KEY=your_openai_key     # Opcional
ANTHROPIC_API_KEY=your_claude_key  # Opcional
GROQ_API_KEY=your_groq_key        # Opcional

# Variables de autenticación (NEW - Phase 4)
JWT_SECRET_KEY=your_super_secret_key_here      # REQUERIDO - Firma JWT
JWT_ALGORITHM=HS256                            # Por defecto
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15             # Token corto para seguridad
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7                # Refresh token más largo
MONGODB_ENVIRONMENT=development                # Aislamiento de base de datos

# Configuración del servidor
API_HOST=0.0.0.0                  # Por defecto
API_PORT=8000                     # Por defecto
```

### **Frontend Requirements**
```bash
# Navegar al directorio web
cd web/
 
# Instalar dependencias
npm install

# Variables de entorno
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# Desarrollo
npm run dev  # Puerto 9002 con Turbopack

# Build para producción
npm run build
npm start
```

### **Project Structure**
```
AgentQA/
├── src/                      # Backend Python
│   ├── API/                  # FastAPI routes modulares
│   ├── Agents/              # Agentes de IA
│   ├── Config/              # Configuraciones
│   ├── Core/                # Funcionalidades core
│   ├── Utilities/           # Utilidades y servicios
│   └── Observability/       # Monitoreo y logs
├── web/                     # Frontend Next.js (PRINCIPAL)
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # React components
│   │   ├── lib/             # Utilities y API clients
│   │   └── types/           # TypeScript definitions
│   ├── public/              # Static assets
│   └── package.json         # Dependencies y scripts
├── prompts/                 # Templates de prompts
├── projects/                # Datos de proyectos
├── tests/                   # Historial de ejecuciones
├── memory-bank/             # Contexto para Copilot
└── browseruse-Docs/         # Documentación browser-use
```

## Technical Constraints

### **AI Model Dependencies**
*   **Primary:** Google Gemini (requires GOOGLE_API_KEY)
*   **Fallbacks:** OpenAI GPT, Anthropic Claude, Groq
*   **Limitation:** Sin API keys, funcionalidad limitada

### **Browser Automation Constraints**
*   **browser-use v0.2.5:** Versión específica para compatibilidad
*   **Headless Mode:** Configurable, pero requerido para producción
*   **Vision Capabilities:** Depende del modelo de IA seleccionado
*   **Network Requirements:** Acceso a internet para ejecución de pruebas

### **Performance Considerations**
*   **Concurrent Execution:** FastAPI soporta operaciones asíncronas
*   **Memory Usage:** Ejecuciones de navegador pueden ser intensivas
*   **API Rate Limits:** Limitado por quotas de proveedores de IA
*   **Storage:** Historial de pruebas incluye screenshots (espacio)

### **Security Constraints**
*   **API Keys:** Manejo seguro de credenciales de IA
*   **CORS:** Configurado para desarrollo local
*   **File System:** Acceso controlado para lectura/escritura de proyectos
*   **Browser Security:** Opción para deshabilitar seguridad en pruebas
*   **Authentication Security (NEW - Phase 4):**
    *   JWT Secret Key: Must be cryptographically secure (256-bit minimum)
    *   Password Policy: Minimum 8 characters, bcrypt hashing with salt rounds 12
    *   Token Expiration: Short-lived access tokens (15min) for security
    *   Rate Limiting: Login attempts limited to prevent brute force attacks
    *   Multi-Tenancy: Strict organization_id isolation in all database queries
    *   HTTPS Required: Production must use HTTPS for token transmission
    *   Session Security: httpOnly cookies for refresh tokens
    *   CSRF Protection: Anti-CSRF tokens for state-changing operations

### **Platform Compatibility**
*   **Backend:** Cross-platform Python 3.8+
*   **Frontend:** Navegadores modernos con soporte ES2017+
*   **Browser Automation:** Depende de Chromium/Chrome disponible
*   **CLI:** Compatible con shells Unix-like y Windows

### **Development Constraints**
*   **TypeScript:** Configurado con strict mode
*   **ESLint/Build:** Errores ignorados para desarrollo rápido
*   **Hot Reload:** Turbopack habilitado para desarrollo
*   **API Documentation:** Auto-generada, accesible en /docs

### **Internationalization**
*   **Backend:** Inglés para ejecución (browser-use compatibility)
*   **Frontend:** Soporte español/inglés
*   **Translation API:** OpenAI para traducción automática
*   **User Interface:** Idioma configurable por usuario
