# QAK Multi-Tenant Authentication System - UI Testing Guide

## 📋 Overview

This comprehensive testing guide covers all phases of the QAK multi-tenant authentication system from the user interface perspective. Follow these steps to verify that all authentication, multi-tenancy, and RBAC features work correctly through the web browser.

## 🚀 Pre-Testing Setup

### Prerequisites
Before starting the testing process, ensure the following components are running:

#### 1. **Backend API Server**
```bash
cd /path/to/qak
python -m uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```
- **Expected**: API server running on `http://localhost:8000`
- **Verify**: Navigate to `http://localhost:8000/docs` to see FastAPI documentation

#### 2. **Database (MongoDB)**
```bash
# If using Docker
docker run -d -p 27017:27017 --name qak-mongo mongo:latest

# Or start your local MongoDB instance
mongod --dbpath /path/to/your/db
```
- **Expected**: MongoDB running on `mongodb://localhost:27017`
- **Verify**: Can connect to database (check logs for connection success)

#### 3. **Frontend Development Server**
```bash
cd /path/to/qak/web
npm install  # if not already done
npm run dev
```
- **Expected**: Next.js dev server running on `http://localhost:3000`
- **Verify**: Navigate to `http://localhost:3000` to see QAK homepage

#### 4. **Environment Configuration**
Ensure these environment variables are set:
```bash
# Backend (.env)
JWT_SECRET_KEY=your_secret_key_here
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
MONGODB_URL=mongodb://localhost:27017/qak_test

# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Initial State Verification
- **Database**: Should be empty or have minimal test data
- **Browser**: Clear localStorage and cookies for `localhost:3000`
- **Network**: Ensure no proxy/VPN interfering with localhost connections

---

## 🧪 Phase 1: Foundation Testing via UI

### Test 1.1: Password Strength Validation

**Objective**: Verify password validation works in real-time during registration

**Steps**:
1. Navigate to `http://localhost:3000/auth/register`
2. Fill in basic information (name, email, organization)
3. Focus on the password field and start typing

**Test Cases**:
| Password | Expected Behavior |
|----------|------------------|
| `123` | Red progress bar, "Weak" label, multiple requirement failures |
| `password` | Yellow progress bar, "Medium" label, missing uppercase/number/special |
| `Password123!` | Green progress bar, "Strong" label, all requirements met |

**Expected UI Elements**:
- Password strength progress bar (red/yellow/green)
- Real-time requirement checklist with checkmarks/X marks
- Requirements: 8+ chars, uppercase, lowercase, number, special character

**Success Criteria**: 
- Progress bar color changes appropriately
- Requirement checklist updates in real-time
- Form submission blocked for weak passwords

### Test 1.2: Email Validation

**Objective**: Verify email format validation

**Steps**:
1. On registration form, test various email formats in the email field

**Test Cases**:
| Email | Expected Result |
|-------|----------------|
| `invalid-email` | Error: "Please enter a valid email address" |
| `test@` | Error: "Please enter a valid email address" |
| `<EMAIL>` | ✅ Valid, no error |
| `<EMAIL>` | ✅ Valid, no error |

**Success Criteria**: 
- Invalid emails show error messages
- Valid emails allow form progression
- Error messages are clear and helpful

### Test 1.3: Form Validation Integration

**Objective**: Verify all form validations work together

**Steps**:
1. Try to submit registration form with various invalid combinations
2. Verify error messages appear appropriately
3. Test form reset functionality

**Success Criteria**:
- Multiple validation errors can appear simultaneously
- Form submission is blocked until all validations pass
- Error messages are user-friendly and specific

---

## 🔐 Phase 2: Authentication API Testing via UI

### Test 2.1: User Registration Flow

**Objective**: Complete user registration with organization creation

**Test Data**:
```
First Name: John
Last Name: Doe
Email: <EMAIL>
Password: TestPassword123!
Organization Name: Test Company Inc
```

**Steps**:
1. Navigate to `http://localhost:3000/auth/register`
2. Fill in all required fields with test data above
3. Click "Create account" button
4. Wait for processing

**Expected Behavior**:
- Form shows loading state with spinner
- Button text changes to "Creating account..."
- On success: Automatic redirect to `/dashboard`
- User should be logged in automatically

**UI Verification**:
- Header shows user profile dropdown with "John Doe"
- Organization switcher shows "Test Company Inc"
- No login/signup buttons visible (replaced with user menu)

**Troubleshooting**:
- If stuck on loading: Check browser network tab for API errors
- If redirect fails: Check browser console for JavaScript errors
- If 500 error: Verify backend server is running and database is accessible

### Test 2.2: User Login Flow

**Objective**: Test login with existing credentials

**Steps**:
1. If logged in, logout first (click profile dropdown → "Sign out")
2. Navigate to `http://localhost:3000/auth/login`
3. Enter credentials from Test 2.1
4. Click "Sign in" button

**Expected Behavior**:
- Form shows loading state
- Button text changes to "Signing in..."
- On success: Redirect to `/dashboard`
- User context restored (profile, organization)

**Error Testing**:
| Scenario | Input | Expected Result |
|----------|-------|----------------|
| Wrong password | Correct email + wrong password | "Invalid email or password" error |
| Wrong email | Non-existent email + any password | "Invalid email or password" error |
| Empty fields | Leave fields blank | Field-specific validation errors |

### Test 2.3: Token Refresh Testing

**Objective**: Verify automatic token refresh works

**Steps**:
1. Login successfully
2. Open browser developer tools → Application → Local Storage
3. Note the `qak_access_token` value
4. Wait 16+ minutes (token expires after 15 minutes)
5. Perform any action that requires authentication (navigate to organization page)

**Expected Behavior**:
- Page should load normally without requiring re-login
- New access token should appear in localStorage
- No authentication errors in console

**Manual Token Expiry Test**:
1. Login successfully
2. In browser console, run: `localStorage.setItem('qak_access_token', 'invalid_token')`
3. Navigate to `/organization`
4. Should automatically refresh token or redirect to login

### Test 2.4: Logout Flow

**Objective**: Verify complete logout functionality

**Steps**:
1. While logged in, click user profile dropdown
2. Click "Sign out"
3. Wait for logout process

**Expected Behavior**:
- Loading spinner appears briefly
- Automatic redirect to `/auth/login`
- All authentication data cleared from localStorage
- Header shows login/signup buttons again
- Attempting to access protected pages redirects to login

**Verification**:
- Check localStorage: `qak_access_token`, `qak_refresh_token`, `qak_user_data` should be removed
- Navigate to `/organization` → should redirect to `/auth/login`

---

## 🏢 Phase 3: Multi-Tenancy & RBAC Testing via UI

### Test 3.1: Organization Creation and Management

**Objective**: Test organization admin capabilities

**Setup**: Use the account created in Test 2.1 (John Doe - should be ORG_ADMIN)

**Steps**:
1. Navigate to `http://localhost:3000/organization`
2. Verify organization dashboard loads

**Expected UI Elements**:
- Organization info card showing "Test Company Inc"
- Member list showing John Doe with "ORG_ADMIN" badge
- "Invite User" button visible
- "Settings" button visible

### Test 3.2: User Invitation Flow

**Objective**: Test inviting users to organization

**Test Data**:
```
Email: <EMAIL>
Role: USER
```

**Steps**:
1. On organization page, click "Invite User" button
2. Fill in email and select "USER" role
3. Click "Send Invitation"

**Expected Behavior**:
- Dialog shows role descriptions
- Success message appears: "Invitation <NAME_EMAIL> successfully!"
- Dialog closes automatically after 2 seconds

**Note**: Since email sending isn't implemented, this tests the UI flow. In production, the user would receive an email.

### Test 3.3: Role-Based Access Control Testing

**Objective**: Verify different roles see appropriate UI elements

#### Test 3.3a: ORG_ADMIN Role (Current User)
**Verification Checklist**:
- ✅ Can see "Invite User" button
- ✅ Can see "Settings" button  
- ✅ Can see user management dropdown menus
- ✅ Can access `/organization/settings`

#### Test 3.3b: Create USER Role Account
**Steps**:
1. Open incognito/private browser window
2. Register new user:
   ```
   Name: Jane Smith
   Email: <EMAIL>
   Password: UserPassword123!
   Organization: Test Company Inc (should join existing)
   ```

**Expected Behavior**:
- Should join existing organization as USER role
- Different UI permissions than ORG_ADMIN

#### Test 3.3c: USER Role Verification
**In the USER account, verify**:
- ❌ Cannot see "Invite User" button
- ❌ Cannot see "Settings" button
- ❌ Cannot see user management dropdowns
- ❌ Cannot access `/organization/settings` (should show access denied)

### Test 3.4: Organization Switching

**Objective**: Test switching between organizations (if user belongs to multiple)

**Setup**: Create second organization with existing user

**Steps**:
1. While logged in as John Doe, register for another organization
2. Click organization switcher in header
3. Select different organization

**Expected Behavior**:
- Dropdown shows all user's organizations
- Current organization marked with checkmark
- Switching updates entire app context
- Page refreshes with new organization data

### Test 3.5: Permission-Based UI Rendering

**Objective**: Verify UI elements appear/disappear based on permissions

**Test Matrix**:
| Feature | USER | ORG_ADMIN | ADMIN |
|---------|------|-----------|-------|
| View organization members | ✅ | ✅ | ✅ |
| Invite users | ❌ | ✅ | ✅ |
| Change user roles | ❌ | ✅ | ✅ |
| Remove users | ❌ | ✅ | ✅ |
| Organization settings | ❌ | ✅ | ✅ |
| Admin panel | ❌ | ❌ | ✅ |

**Testing Method**:
1. Login with each role type
2. Navigate to organization page
3. Verify appropriate buttons/menus are visible/hidden

---

## 🎨 Phase 4: Frontend Integration Testing

### Test 4.1: Protected Routes

**Objective**: Verify route protection works correctly

**Test Cases**:
| URL | Unauthenticated | USER | ORG_ADMIN | ADMIN |
|-----|----------------|------|-----------|-------|
| `/dashboard` | → `/auth/login` | ✅ Access | ✅ Access | ✅ Access |
| `/organization` | → `/auth/login` | ✅ Access | ✅ Access | ✅ Access |
| `/organization/settings` | → `/auth/login` | ❌ Access Denied | ✅ Access | ✅ Access |
| `/admin` | → `/auth/login` | ❌ Access Denied | ❌ Access Denied | ✅ Access |

**Testing Steps**:
1. Test each URL while logged out
2. Test each URL with different role accounts
3. Verify redirects and access denied pages

### Test 4.2: Authentication State Persistence

**Objective**: Verify authentication persists across browser sessions

**Steps**:
1. Login successfully
2. Close browser completely
3. Reopen browser and navigate to `http://localhost:3000`
4. Navigate to protected page

**Expected Behavior**:
- Should remain logged in
- No need to re-authenticate
- All user context preserved

### Test 4.3: Responsive Design Testing

**Objective**: Verify authentication UI works on different screen sizes

**Test Viewports**:
- Mobile: 375px width
- Tablet: 768px width  
- Desktop: 1200px width

**Elements to Test**:
- Login/registration forms
- User profile dropdown
- Organization switcher
- Organization management tables

**Expected Behavior**:
- All elements remain functional
- Text remains readable
- Buttons remain clickable
- Tables scroll horizontally on mobile

### Test 4.4: Error Handling and Loading States

**Objective**: Verify proper error handling throughout the UI

**Test Scenarios**:

#### Network Error Simulation
1. Disconnect internet/stop backend server
2. Try to login
3. **Expected**: Clear error message, not generic failure

#### Rate Limiting Test
1. Make multiple rapid login attempts with wrong password
2. **Expected**: Rate limiting message appears

#### Token Expiry Handling
1. Manually expire token (edit localStorage)
2. Navigate to protected page
3. **Expected**: Automatic refresh or graceful redirect to login

---

## 🔄 End-to-End User Journey Testing

### Journey 1: New Organization Setup

**Scenario**: Complete new organization onboarding

**Steps**:
1. **Registration** (`/auth/register`)
   - Register as: `<EMAIL>` / `AdminPass123!`
   - Organization: "New Company LLC"
   - Verify automatic login and redirect

2. **Organization Setup** (`/organization`)
   - Verify organization dashboard loads
   - Check organization info is correct
   - Note user has ORG_ADMIN role

3. **Settings Configuration** (`/organization/settings`)
   - Update organization description
   - Verify changes save successfully
   - Check read-only fields display correctly

4. **Team Member Invitation** (`/organization`)
   - Invite `<EMAIL>` as USER
   - Invite `<EMAIL>` as ORG_ADMIN
   - Verify success messages

**Success Criteria**:
- Smooth flow without errors
- All data persists correctly
- Appropriate permissions at each step

### Journey 2: Multi-User Collaboration

**Scenario**: Multiple users working in same organization

**Setup**: Use organization from Journey 1

**Steps**:
1. **Second User Registration** (Incognito window)
   - Register as: `<EMAIL>` / `UserPass123!`
   - Should join existing "New Company LLC"
   - Verify USER role assigned

2. **Permission Verification**
   - <NAME_EMAIL>
   - Navigate to `/organization`
   - Verify limited permissions (no invite/settings buttons)
   - Try accessing `/organization/settings` → should be denied

3. **Role Management** (Original admin window)
   - <NAME_EMAIL>
   - Navigate to `/organization`
   - Find <EMAIL> in member list
   - Change role from USER to ORG_ADMIN

4. **Permission Update Verification**
   - In user1 window, refresh page
   - Should now see ORG_ADMIN permissions
   - Should be able to access settings

**Success Criteria**:
- Role changes reflect immediately
- Permissions update correctly
- No data leakage between organizations

### Journey 3: Organization Switching

**Scenario**: User belongs to multiple organizations

**Setup**: Create user with access to multiple organizations

**Steps**:
1. **Create Second Organization**
   - Register new account: `<EMAIL>` / `MultiPass123!`
   - Organization: "Second Company"

2. **Join First Organization**
   - Have admin@newcompany.<NAME_EMAIL>
   - Accept invitation (simulate by direct role assignment)

3. **Organization Switching Test**
   - <NAME_EMAIL>
   - Click organization switcher in header
   - Should see both organizations listed
   - Switch between organizations
   - Verify data changes appropriately

**Success Criteria**:
- Organization switcher shows all user's organizations
- Switching updates all page data
- No cross-organization data leakage

---

## 🐛 Common Issues and Troubleshooting

### Authentication Issues

**Problem**: Login fails with network error
**Solutions**:
- Verify backend server is running on port 8000
- Check CORS configuration allows localhost:3000
- Verify database connection

**Problem**: Token refresh fails
**Solutions**:
- Check JWT_SECRET_KEY matches between sessions
- Verify token expiry times are reasonable
- Clear localStorage and re-login

**Problem**: Protected routes not working
**Solutions**:
- Verify AuthProvider wraps entire app
- Check ProtectedRoute component implementation
- Verify token is being sent in requests

### UI/UX Issues

**Problem**: Forms not submitting
**Solutions**:
- Check browser console for JavaScript errors
- Verify form validation is passing
- Check network tab for failed API calls

**Problem**: Organization switcher not working
**Solutions**:
- Verify user belongs to multiple organizations
- Check organization data in localStorage
- Verify API endpoints return correct data

**Problem**: Role-based UI not updating
**Solutions**:
- Verify user role is correctly stored
- Check permission checking logic
- Clear cache and refresh page

### Performance Issues

**Problem**: Slow page loads
**Solutions**:
- Check for unnecessary API calls
- Verify React Query caching is working
- Monitor network tab for large payloads

**Problem**: Memory leaks
**Solutions**:
- Verify useEffect cleanup functions
- Check for unclosed subscriptions
- Monitor browser memory usage

---

## ✅ Testing Checklist Summary

### Pre-Testing Setup
- [ ] Backend API server running (port 8000)
- [ ] MongoDB database running (port 27017)
- [ ] Frontend dev server running (port 3000)
- [ ] Environment variables configured
- [ ] Browser localStorage cleared

### Phase 1: Foundation
- [ ] Password strength validation works
- [ ] Email format validation works
- [ ] Form validation integration works

### Phase 2: Authentication API
- [ ] User registration flow complete
- [ ] User login flow works
- [ ] Token refresh automatic
- [ ] Logout clears all data

### Phase 3: Multi-Tenancy & RBAC
- [ ] Organization creation works
- [ ] User invitation flow works
- [ ] Role-based access control enforced
- [ ] Organization switching works
- [ ] Permission-based UI rendering works

### Phase 4: Frontend Integration
- [ ] Protected routes work correctly
- [ ] Authentication state persists
- [ ] Responsive design works
- [ ] Error handling comprehensive

### End-to-End Journeys
- [ ] New organization setup complete
- [ ] Multi-user collaboration works
- [ ] Organization switching seamless

---

## 📊 Success Metrics

**Authentication System is Ready When**:
- ✅ All test cases pass without errors
- ✅ User experience is smooth and intuitive
- ✅ Security measures are properly enforced
- ✅ Multi-tenancy isolation is complete
- ✅ Role-based access control works correctly
- ✅ Performance is acceptable (< 2s page loads)
- ✅ Error messages are user-friendly
- ✅ Mobile experience is fully functional

**Ready for Production Deployment!** 🚀
