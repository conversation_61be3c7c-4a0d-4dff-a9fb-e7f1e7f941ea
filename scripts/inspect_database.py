#!/usr/bin/env python3
"""
Database inspection tool for QAK authentication system

This tool allows you to inspect users, organizations, and relationships
in the MongoDB database to debug authentication issues.
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import motor.motor_asyncio
from datetime import datetime
import json

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / ".env")


async def inspect_database():
    """Inspect QAK database contents."""
    
    print("🔍 QAK Database Inspector")
    print("=" * 60)
    
    try:
        # Connect to database
        mongodb_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
        mongodb_env = os.getenv("MONGODB_ENVIRONMENT", "development")
        
        # Configure client with SSL settings for Atlas
        client_options = {}
        if "mongodb+srv://" in mongodb_uri:
            client_options["tls"] = True
            client_options["tlsAllowInvalidCertificates"] = True
        
        client = motor.motor_asyncio.AsyncIOMotorClient(mongodb_uri, **client_options)
        db_name = f"qak_{mongodb_env}"
        db = client[db_name]
        
        print(f"🔗 Database: {db_name}")
        print(f"📡 MongoDB URI: {mongodb_uri}")
        
        # Test connection
        await client.admin.command('ping')
        print("✅ Database connection successful\n")
        
        # Get all collections
        collections = await db.list_collection_names()
        print(f"📊 Available Collections: {collections}\n")
        
        # Inspect Users
        print("👥 USERS COLLECTION")
        print("-" * 40)
        users = await db.users.find({}).to_list(length=None)
        
        if not users:
            print("❌ No users found")
        else:
            print(f"✅ Found {len(users)} users:")
            for i, user in enumerate(users, 1):
                print(f"\n{i}. User:")
                print(f"   📧 Email: {user.get('email', 'N/A')}")
                print(f"   👤 Name: {user.get('first_name', 'N/A')} {user.get('last_name', 'N/A')}")
                print(f"   🆔 User ID: {user.get('user_id', 'N/A')}")
                print(f"   ✅ Active: {user.get('is_active', 'N/A')}")
                print(f"   📧 Email Verified: {user.get('email_verified', 'N/A')}")
                print(f"   📅 Created: {user.get('created_at', 'N/A')}")
                print(f"   🔐 Has Password: {'Yes' if user.get('hashed_password') else 'No'}")
        
        # Inspect Organizations
        print(f"\n🏢 ORGANIZATIONS COLLECTION")
        print("-" * 40)
        orgs = await db.organizations.find({}).to_list(length=None)
        
        if not orgs:
            print("❌ No organizations found")
        else:
            print(f"✅ Found {len(orgs)} organizations:")
            for i, org in enumerate(orgs, 1):
                print(f"\n{i}. Organization:")
                print(f"   🏢 Name: {org.get('name', 'N/A')}")
                print(f"   🔗 Slug: {org.get('slug', 'N/A')}")
                print(f"   🆔 Org ID: {org.get('organization_id', 'N/A')}")
                print(f"   📝 Description: {org.get('description', 'N/A')}")
                print(f"   ✅ Active: {org.get('is_active', 'N/A')}")
                print(f"   📅 Created: {org.get('created_at', 'N/A')}")
                print(f"   ⚙️  Settings: {org.get('settings', {})}")
        
        # Inspect User-Organization Relationships
        print(f"\n🔗 USER-ORGANIZATION RELATIONSHIPS")
        print("-" * 40)
        user_orgs = await db.user_organizations.find({}).to_list(length=None)
        
        if not user_orgs:
            print("❌ No user-organization relationships found")
        else:
            print(f"✅ Found {len(user_orgs)} relationships:")
            for i, rel in enumerate(user_orgs, 1):
                # Get user and org details
                user = await db.users.find_one({"user_id": rel.get("user_id")})
                org = await db.organizations.find_one({"organization_id": rel.get("organization_id")})
                
                print(f"\n{i}. Relationship:")
                print(f"   👤 User: {user.get('email', 'N/A') if user else 'USER NOT FOUND'}")
                print(f"   🏢 Organization: {org.get('name', 'N/A') if org else 'ORG NOT FOUND'}")
                print(f"   👑 Role: {rel.get('role', 'N/A')}")
                print(f"   📅 Joined: {rel.get('joined_at', 'N/A')}")
                print(f"   🆔 User ID: {rel.get('user_id', 'N/A')}")
                print(f"   🆔 Org ID: {rel.get('organization_id', 'N/A')}")
        
        # Test specific user lookup
        print(f"\n🔍 ADMIN USER LOOKUP")
        print("-" * 40)
        admin_email = os.getenv("ADMIN_EMAIL", "<EMAIL>")
        admin_user = await db.users.find_one({"email": admin_email})
        
        if admin_user:
            print(f"✅ Found admin user: {admin_email}")
            admin_rel = await db.user_organizations.find_one({"user_id": admin_user["user_id"]})
            if admin_rel:
                admin_org = await db.organizations.find_one({"organization_id": admin_rel["organization_id"]})
                print(f"   👑 Role: {admin_rel['role']}")
                print(f"   🏢 Organization: {admin_org['name'] if admin_org else 'ORG NOT FOUND'}")
            else:
                print("   ❌ No organization relationship found")
        else:
            print(f"❌ Admin user not found: {admin_email}")
        
        # Database statistics
        print(f"\n📊 DATABASE STATISTICS")
        print("-" * 40)
        stats = {
            "users": len(users),
            "organizations": len(orgs),
            "user_organizations": len(user_orgs),
            "database_name": db_name,
            "connection_type": "Atlas" if "mongodb+srv" in mongodb_uri else "Local"
        }
        
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # Check for common issues
        print(f"\n🚨 POTENTIAL ISSUES")
        print("-" * 40)
        issues = []
        
        # Check for users without organizations
        for user in users:
            user_rel = await db.user_organizations.find_one({"user_id": user["user_id"]})
            if not user_rel:
                issues.append(f"User {user['email']} has no organization")
        
        # Check for orphaned relationships
        for rel in user_orgs:
            user = await db.users.find_one({"user_id": rel["user_id"]})
            org = await db.organizations.find_one({"organization_id": rel["organization_id"]})
            if not user:
                issues.append(f"Orphaned relationship: user_id {rel['user_id']} not found")
            if not org:
                issues.append(f"Orphaned relationship: organization_id {rel['organization_id']} not found")
        
        if issues:
            for issue in issues:
                print(f"   ⚠️  {issue}")
        else:
            print("   ✅ No issues detected")
        
        # Export data option
        print(f"\n💾 EXPORT DATA")
        print("-" * 40)
        export_data = {
            "users": users,
            "organizations": orgs,
            "user_organizations": user_orgs,
            "exported_at": datetime.now().isoformat(),
            "database": db_name
        }
        
        export_file = project_root / "database_export.json"
        with open(export_file, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        print(f"✅ Data exported to: {export_file}")
        
    except Exception as e:
        print(f"❌ Error inspecting database: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        client.close()


async def test_user_login(email: str, password: str):
    """Test if a user can login with given credentials."""
    
    print(f"\n🔐 TESTING LOGIN: {email}")
    print("-" * 40)
    
    try:
        # Import password service
        from src.services.auth.password_service import PasswordService
        password_service = PasswordService()
        
        # Connect to database
        mongodb_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
        mongodb_env = os.getenv("MONGODB_ENVIRONMENT", "development")
        
        client_options = {}
        if "mongodb+srv://" in mongodb_uri:
            client_options["tls"] = True
            client_options["tlsAllowInvalidCertificates"] = True
        
        client = motor.motor_asyncio.AsyncIOMotorClient(mongodb_uri, **client_options)
        db_name = f"qak_{mongodb_env}"
        db = client[db_name]
        
        # Find user
        user = await db.users.find_one({"email": email})
        if not user:
            print(f"❌ User not found: {email}")
            return False
        
        print(f"✅ User found: {email}")
        print(f"   Active: {user.get('is_active', False)}")
        print(f"   Email Verified: {user.get('email_verified', False)}")
        
        # Test password
        stored_hash = user.get("hashed_password")
        if not stored_hash:
            print("❌ No password hash stored")
            return False
        
        is_valid = password_service.verify_password(password, stored_hash)
        print(f"   Password Valid: {is_valid}")
        
        if is_valid:
            print("✅ Login test PASSED")
        else:
            print("❌ Login test FAILED - Invalid password")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ Error testing login: {e}")
        return False
    finally:
        client.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Inspect QAK database")
    parser.add_argument("--test-login", nargs=2, metavar=("EMAIL", "PASSWORD"), 
                       help="Test login with email and password")
    
    args = parser.parse_args()
    
    if args.test_login:
        email, password = args.test_login
        asyncio.run(test_user_login(email, password))
    else:
        asyncio.run(inspect_database())
