#!/usr/bin/env python3
"""
Test script to verify QAK backend authentication endpoints

This script tests the backend API endpoints to ensure they're working correctly.
"""

import requests
import json
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
project_root = Path(__file__).parent.parent
load_dotenv(project_root / ".env")

def test_backend():
    """Test QAK backend authentication endpoints."""
    
    print("🧪 Testing QAK Backend Authentication")
    print("=" * 50)
    
    # API configuration
    api_url = "http://localhost:8000"
    
    try:
        # Test 1: Health check
        print("1. Testing API Health...")
        health_response = requests.get(f"{api_url}/health", timeout=5)
        
        if health_response.status_code == 200:
            print("   ✅ API Health: OK")
            health_data = health_response.json()
            print(f"   📊 Status: {health_data.get('status', 'unknown')}")
            print(f"   🔑 API Key Configured: {health_data.get('api_key_configured', 'unknown')}")
        else:
            print(f"   ❌ API Health: Failed ({health_response.status_code})")
            return False
        
        # Test 2: Check auth endpoints exist
        print("\n2. Testing Auth Endpoints...")
        
        # Test registration endpoint (should return 422 for missing data)
        reg_response = requests.post(f"{api_url}/auth/register", json={}, timeout=5)
        if reg_response.status_code == 422:
            print("   ✅ Registration endpoint: Available")
        else:
            print(f"   ❌ Registration endpoint: Unexpected status {reg_response.status_code}")
        
        # Test login endpoint (should return 422 for missing data)
        login_response = requests.post(f"{api_url}/auth/login", json={}, timeout=5)
        if login_response.status_code == 422:
            print("   ✅ Login endpoint: Available")
        else:
            print(f"   ❌ Login endpoint: Unexpected status {login_response.status_code}")
        
        # Test 3: Try to register a test user
        print("\n3. Testing User Registration...")
        test_user = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        reg_test_response = requests.post(
            f"{api_url}/auth/register",
            json=test_user,
            timeout=10
        )
        
        if reg_test_response.status_code == 201:
            print("   ✅ Test user registration: SUCCESS")
            reg_data = reg_test_response.json()
            print(f"   👤 User: {reg_data.get('user', {}).get('email', 'N/A')}")
            print(f"   🏢 Organization: {reg_data.get('organization', {}).get('name', 'N/A')}")
            
            # Test 4: Try to login with the test user
            print("\n4. Testing User Login...")
            login_test_response = requests.post(
                f"{api_url}/auth/login",
                json={
                    "email": test_user["email"],
                    "password": test_user["password"]
                },
                timeout=10
            )
            
            if login_test_response.status_code == 200:
                print("   ✅ Test user login: SUCCESS")
                login_data = login_test_response.json()
                print(f"   🔑 Access Token: {'Present' if login_data.get('access_token') else 'Missing'}")
                print(f"   🔄 Refresh Token: {'Present' if login_data.get('refresh_token') else 'Missing'}")
            else:
                print(f"   ❌ Test user login: Failed ({login_test_response.status_code})")
                try:
                    error_data = login_test_response.json()
                    print(f"   📝 Error: {error_data.get('detail', 'Unknown error')}")
                except:
                    print(f"   📝 Response: {login_test_response.text}")
            
        elif reg_test_response.status_code == 400:
            error_data = reg_test_response.json()
            if "already exists" in error_data.get("detail", "").lower():
                print("   ⚠️  Test user already exists, testing login...")
                
                # Test login with existing user
                login_test_response = requests.post(
                    f"{api_url}/auth/login",
                    json={
                        "email": test_user["email"],
                        "password": test_user["password"]
                    },
                    timeout=10
                )
                
                if login_test_response.status_code == 200:
                    print("   ✅ Existing test user login: SUCCESS")
                else:
                    print(f"   ❌ Existing test user login: Failed ({login_test_response.status_code})")
            else:
                print(f"   ❌ Test user registration: {error_data.get('detail', 'Unknown error')}")
        else:
            print(f"   ❌ Test user registration: Failed ({reg_test_response.status_code})")
            try:
                error_data = reg_test_response.json()
                print(f"   📝 Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   📝 Response: {reg_test_response.text}")
        
        # Test 5: Test admin user login
        print("\n5. Testing Admin User Login...")
        admin_email = os.getenv("ADMIN_EMAIL", "<EMAIL>")
        admin_password = os.getenv("ADMIN_PASSWORD", "AdminPassword123!")
        
        admin_login_response = requests.post(
            f"{api_url}/auth/login",
            json={
                "email": admin_email,
                "password": admin_password
            },
            timeout=10
        )
        
        if admin_login_response.status_code == 200:
            print("   ✅ Admin user login: SUCCESS")
            admin_data = admin_login_response.json()
            print(f"   👤 Admin: {admin_data.get('user', {}).get('email', 'N/A')}")
            print(f"   👑 Role: {admin_data.get('user_organization', {}).get('role', 'N/A')}")
        else:
            print(f"   ❌ Admin user login: Failed ({admin_login_response.status_code})")
            try:
                error_data = admin_login_response.json()
                print(f"   📝 Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   📝 Response: {admin_login_response.text}")
            print(f"   💡 Try running: python scripts/create_admin_fixed.py")
        
        print("\n🎉 Backend testing completed!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to backend API")
        print("💡 Make sure the backend is running:")
        print("   uvicorn src.main:app --reload --port 8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend API request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_backend()
