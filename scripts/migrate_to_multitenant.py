#!/usr/bin/env python3
"""
Multi-Tenant Migration Script for QAK

Migrates existing QAK data to multi-tenant architecture by:
1. Creating a default organization for existing data
2. Adding organization_id field to all existing documents
3. Creating necessary indexes for performance
4. Backing up existing data before migration

Usage:
    python scripts/migrate_to_multitenant.py [--dry-run] [--backup-dir /path/to/backup]
"""

import asyncio
import argparse
import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import QAK modules
from src.database.connection import initialize_database, get_database_manager
from src.database.odm import initialize_odm, register_models
from src.database.models import (
    Project, Execution, CodegenSession, Artifact, 
    User, Organization, UserOrganization, Role
)
from src.services.auth.token_blacklist import BlacklistedToken
from motor.motor_asyncio import AsyncIOMotorDatabase
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MultiTenantMigration:
    """Handles migration to multi-tenant architecture."""
    
    def __init__(self, dry_run: bool = False, backup_dir: Optional[str] = None):
        """
        Initialize migration.
        
        Args:
            dry_run: If True, only simulate migration without making changes
            backup_dir: Directory to store backups (optional)
        """
        self.dry_run = dry_run
        self.backup_dir = backup_dir
        self.db: Optional[AsyncIOMotorDatabase] = None
        self.default_org: Optional[Organization] = None
        
        # Collections that need organization_id field
        self.collections_to_migrate = [
            "projects",      # Project documents (currently BaseModel, not Document)
            "executions",    # Execution documents
            "codegen_sessions",  # CodegenSession documents
            "artifacts",     # Artifact documents
        ]
        
        # Statistics
        self.migration_stats = {
            "collections_migrated": 0,
            "documents_updated": 0,
            "indexes_created": 0,
            "backup_files_created": 0,
            "errors": [],
        }
    
    async def initialize(self):
        """Initialize database connection and ODM."""
        logger.info("Initializing database connection...")
        
        # Initialize database
        await initialize_database()
        db_manager = get_database_manager()
        self.db = db_manager.get_database()
        
        # Initialize ODM
        models = [Execution, CodegenSession, Artifact, User, Organization, UserOrganization, BlacklistedToken]
        register_models(models)
        await initialize_odm()
        
        logger.info("Database initialized successfully")
    
    async def create_backup(self) -> bool:
        """
        Create backup of existing data.
        
        Returns:
            True if backup successful, False otherwise
        """
        if not self.backup_dir:
            logger.info("No backup directory specified, skipping backup")
            return True
        
        try:
            backup_path = Path(self.backup_dir)
            backup_path.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            for collection_name in self.collections_to_migrate:
                logger.info(f"Backing up collection: {collection_name}")
                
                collection = self.db[collection_name]
                documents = await collection.find({}).to_list(None)
                
                backup_file = backup_path / f"{collection_name}_{timestamp}.json"
                
                with open(backup_file, 'w') as f:
                    json.dump(documents, f, default=str, indent=2)
                
                self.migration_stats["backup_files_created"] += 1
                logger.info(f"Backed up {len(documents)} documents to {backup_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            self.migration_stats["errors"].append(f"Backup failed: {e}")
            return False
    
    async def create_default_organization(self) -> Organization:
        """
        Create default organization for existing data.
        
        Returns:
            Created Organization instance
        """
        logger.info("Creating default organization...")
        
        # Check if default organization already exists
        existing_org = await Organization.find_one({"name": "Default Organization"})
        if existing_org:
            logger.info(f"Default organization already exists: {existing_org.organization_id}")
            return existing_org
        
        if self.dry_run:
            logger.info("[DRY RUN] Would create default organization")
            # Create a mock organization for dry run
            from src.database.models.organization import generate_slug
            return Organization(
                name="Default Organization",
                slug=generate_slug("Default Organization"),
                description="Default organization for migrated data",
                settings={"migration_created": True}
            )
        
        # Create default organization
        organization = Organization.create_with_auto_slug(
            name="Default Organization",
            description="Default organization for migrated data",
            settings={"migration_created": True}
        )
        
        await organization.insert()
        logger.info(f"Created default organization: {organization.organization_id}")
        
        return organization
    
    async def migrate_collection(self, collection_name: str, organization_id: str) -> int:
        """
        Migrate a collection to include organization_id.
        
        Args:
            collection_name: Name of collection to migrate
            organization_id: Organization ID to assign to documents
            
        Returns:
            Number of documents updated
        """
        logger.info(f"Migrating collection: {collection_name}")
        
        collection = self.db[collection_name]
        
        # Find documents without organization_id
        query = {"organization_id": {"$exists": False}}
        documents = await collection.find(query).to_list(None)
        
        if not documents:
            logger.info(f"No documents to migrate in {collection_name}")
            return 0
        
        logger.info(f"Found {len(documents)} documents to migrate in {collection_name}")
        
        if self.dry_run:
            logger.info(f"[DRY RUN] Would update {len(documents)} documents in {collection_name}")
            return len(documents)
        
        # Update documents with organization_id
        update_result = await collection.update_many(
            query,
            {
                "$set": {
                    "organization_id": organization_id,
                    "migrated_at": datetime.utcnow(),
                    "migration_version": "1.0.0"
                }
            }
        )
        
        updated_count = update_result.modified_count
        logger.info(f"Updated {updated_count} documents in {collection_name}")
        
        return updated_count
    
    async def create_indexes(self):
        """Create necessary indexes for multi-tenant performance."""
        logger.info("Creating multi-tenant indexes...")
        
        index_definitions = {
            "executions": [
                [("organization_id", 1)],
                [("organization_id", 1), ("project_id", 1)],
                [("organization_id", 1), ("status", 1)],
                [("organization_id", 1), ("started_at", -1)],
            ],
            "codegen_sessions": [
                [("organization_id", 1)],
                [("organization_id", 1), ("status", 1)],
                [("organization_id", 1), ("created_at", -1)],
            ],
            "artifacts": [
                [("organization_id", 1)],
                [("organization_id", 1), ("execution_id", 1)],
                [("organization_id", 1), ("type", 1)],
                [("organization_id", 1), ("collected_at", -1)],
            ],
            "projects": [
                [("organization_id", 1)],
                [("organization_id", 1), ("name", 1)],  # Unique project names per org
                [("organization_id", 1), ("created_at", -1)],
            ]
        }
        
        for collection_name, indexes in index_definitions.items():
            if self.dry_run:
                logger.info(f"[DRY RUN] Would create {len(indexes)} indexes for {collection_name}")
                continue
            
            collection = self.db[collection_name]
            
            for index_spec in indexes:
                try:
                    await collection.create_index(index_spec)
                    self.migration_stats["indexes_created"] += 1
                    logger.info(f"Created index {index_spec} on {collection_name}")
                except Exception as e:
                    if "already exists" in str(e).lower():
                        logger.debug(f"Index {index_spec} already exists on {collection_name}")
                    else:
                        logger.warning(f"Failed to create index {index_spec} on {collection_name}: {e}")
    
    async def verify_migration(self, organization_id: str) -> bool:
        """
        Verify migration was successful.
        
        Args:
            organization_id: Organization ID to verify
            
        Returns:
            True if verification successful, False otherwise
        """
        logger.info("Verifying migration...")
        
        verification_passed = True
        
        for collection_name in self.collections_to_migrate:
            collection = self.db[collection_name]
            
            # Check for documents without organization_id
            missing_org_count = await collection.count_documents({
                "organization_id": {"$exists": False}
            })
            
            if missing_org_count > 0:
                logger.error(f"Found {missing_org_count} documents without organization_id in {collection_name}")
                verification_passed = False
            
            # Check for documents with correct organization_id
            correct_org_count = await collection.count_documents({
                "organization_id": organization_id
            })
            
            logger.info(f"Collection {collection_name}: {correct_org_count} documents with organization_id")
        
        if verification_passed:
            logger.info("Migration verification passed!")
        else:
            logger.error("Migration verification failed!")
        
        return verification_passed
    
    async def run_migration(self) -> bool:
        """
        Run the complete migration process.
        
        Returns:
            True if migration successful, False otherwise
        """
        try:
            logger.info("Starting multi-tenant migration...")
            
            if self.dry_run:
                logger.info("=== DRY RUN MODE - NO CHANGES WILL BE MADE ===")
            
            # Step 1: Create backup
            if not await self.create_backup():
                return False
            
            # Step 2: Create default organization
            self.default_org = await self.create_default_organization()
            
            # Step 3: Migrate collections
            total_updated = 0
            for collection_name in self.collections_to_migrate:
                updated_count = await self.migrate_collection(
                    collection_name, 
                    self.default_org.organization_id
                )
                total_updated += updated_count
                self.migration_stats["documents_updated"] += updated_count
            
            self.migration_stats["collections_migrated"] = len(self.collections_to_migrate)
            
            # Step 4: Create indexes
            await self.create_indexes()
            
            # Step 5: Verify migration (skip for dry run)
            if not self.dry_run:
                if not await self.verify_migration(self.default_org.organization_id):
                    return False
            
            logger.info("Migration completed successfully!")
            self.print_migration_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            self.migration_stats["errors"].append(f"Migration failed: {e}")
            return False
    
    def print_migration_summary(self):
        """Print migration summary statistics."""
        logger.info("=== MIGRATION SUMMARY ===")
        logger.info(f"Collections migrated: {self.migration_stats['collections_migrated']}")
        logger.info(f"Documents updated: {self.migration_stats['documents_updated']}")
        logger.info(f"Indexes created: {self.migration_stats['indexes_created']}")
        logger.info(f"Backup files created: {self.migration_stats['backup_files_created']}")
        
        if self.migration_stats["errors"]:
            logger.error(f"Errors encountered: {len(self.migration_stats['errors'])}")
            for error in self.migration_stats["errors"]:
                logger.error(f"  - {error}")
        else:
            logger.info("No errors encountered!")
        
        if self.default_org:
            logger.info(f"Default organization ID: {self.default_org.organization_id}")
            logger.info(f"Default organization name: {self.default_org.name}")


async def main():
    """Main migration function."""
    parser = argparse.ArgumentParser(description="Migrate QAK to multi-tenant architecture")
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Simulate migration without making changes"
    )
    parser.add_argument(
        "--backup-dir", 
        type=str, 
        help="Directory to store backups (default: ./backups)"
    )
    
    args = parser.parse_args()
    
    # Set default backup directory
    backup_dir = args.backup_dir or "./backups"
    
    # Create migration instance
    migration = MultiTenantMigration(
        dry_run=args.dry_run,
        backup_dir=backup_dir
    )
    
    try:
        # Initialize database
        await migration.initialize()
        
        # Run migration
        success = await migration.run_migration()
        
        if success:
            logger.info("Migration completed successfully!")
            sys.exit(0)
        else:
            logger.error("Migration failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Migration error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
