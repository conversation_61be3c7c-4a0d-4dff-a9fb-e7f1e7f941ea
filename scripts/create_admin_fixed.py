#!/usr/bin/env python3
"""
Fixed script to create admin user without Beanie ODM dependencies

This script directly inserts documents into MongoDB without using Beanie models,
avoiding initialization issues.
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from dotenv import load_dotenv
import motor.motor_asyncio
from uuid import uuid4

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / ".env")

# Import only the password service (no Beanie models)
from src.services.auth.password_service import PasswordService


def generate_uuid():
    """Generate a UUID string."""
    return str(uuid4())


def generate_slug(name: str) -> str:
    """Generate a URL-friendly slug from organization name."""
    import re
    # Convert to lowercase and replace spaces/special chars with hyphens
    slug = re.sub(r'[^\w\s-]', '', name.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')


async def create_admin_user():
    """Create admin user directly in MongoDB."""
    
    print("🚀 Creating QAK Admin User (Fixed Version)...")
    
    # Admin user data from environment
    admin_data = {
        "email": os.getenv("ADMIN_EMAIL", "<EMAIL>"),
        "password": os.getenv("ADMIN_PASSWORD", "AdminPassword123!"),
        "first_name": os.getenv("ADMIN_FIRST_NAME", "QAK"),
        "last_name": os.getenv("ADMIN_LAST_NAME", "Administrator"),
        "organization_name": os.getenv("ADMIN_ORGANIZATION", "QAK System Administration")
    }
    
    try:
        # Connect to database
        mongodb_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
        mongodb_env = os.getenv("MONGODB_ENVIRONMENT", "development")
        
        # Configure client with SSL settings for Atlas
        client_options = {}
        if "mongodb+srv://" in mongodb_uri:
            client_options["tls"] = True
            client_options["tlsAllowInvalidCertificates"] = True
        
        client = motor.motor_asyncio.AsyncIOMotorClient(mongodb_uri, **client_options)
        db_name = f"qak_{mongodb_env}"
        db = client[db_name]
        
        print(f"🔗 Connected to database: {db_name}")
        
        # Test connection
        await client.admin.command('ping')
        print("✅ Database connection successful")
        
        print(f"📧 Creating admin user: {admin_data['email']}")
        
        # Check if admin user already exists
        existing_user = await db.users.find_one({"email": admin_data["email"]})
        if existing_user:
            print(f"⚠️  Admin user {admin_data['email']} already exists!")
            
            # Check if user has admin role
            user_org = await db.user_organizations.find_one({"user_id": existing_user["user_id"]})
            if user_org:
                print(f"   Current role: {user_org['role']}")
                if user_org['role'] != 'ADMIN':
                    # Upgrade to ADMIN role
                    await db.user_organizations.update_one(
                        {"user_id": existing_user["user_id"]},
                        {"$set": {"role": "ADMIN"}}
                    )
                    print("✅ Upgraded existing user to ADMIN role")
            
            print("=" * 50)
            print("📋 Admin Credentials:")
            print(f"   Email: {admin_data['email']}")
            print(f"   Password: {admin_data['password']}")
            print(f"   Role: ADMIN")
            print("=" * 50)
            return
        
        # Initialize password service
        password_service = PasswordService()
        
        # Generate UUIDs
        user_id = generate_uuid()
        organization_id = generate_uuid()
        
        # Create organization document
        print(f"🏢 Creating organization: {admin_data['organization_name']}")
        organization_doc = {
            "organization_id": organization_id,
            "name": admin_data["organization_name"],
            "slug": generate_slug(admin_data["organization_name"]),
            "description": "QAK System Administration Organization",
            "settings": {
                "is_system_org": True,
                "created_by_script": True
            },
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # Insert organization
        await db.organizations.insert_one(organization_doc)
        print(f"✅ Organization created with ID: {organization_id}")
        
        # Hash password
        hashed_password = password_service.hash_password(admin_data["password"])
        
        # Create user document
        user_doc = {
            "user_id": user_id,
            "email": admin_data["email"],
            "hashed_password": hashed_password,
            "first_name": admin_data["first_name"],
            "last_name": admin_data["last_name"],
            "is_active": True,
            "email_verified": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        # Insert user
        await db.users.insert_one(user_doc)
        print(f"✅ Admin user created with ID: {user_id}")
        
        # Create user-organization relationship with ADMIN role
        user_org_doc = {
            "user_id": user_id,
            "organization_id": organization_id,
            "role": "ADMIN",  # Super admin role
            "joined_at": datetime.now(timezone.utc)
        }
        
        # Insert relationship
        await db.user_organizations.insert_one(user_org_doc)
        print(f"✅ ADMIN role assigned to user")
        
        # Create indexes for better performance
        print("📊 Creating database indexes...")
        
        # User indexes
        await db.users.create_index("email", unique=True)
        await db.users.create_index("user_id", unique=True)
        
        # Organization indexes
        await db.organizations.create_index("organization_id", unique=True)
        await db.organizations.create_index("slug", unique=True)
        
        # User-organization indexes
        await db.user_organizations.create_index([("user_id", 1), ("organization_id", 1)], unique=True)
        await db.user_organizations.create_index("user_id")
        await db.user_organizations.create_index("organization_id")
        
        print("✅ Database indexes created")
        
        print("\n🎉 Admin user created successfully!")
        print("=" * 50)
        print("📋 Admin Credentials:")
        print(f"   Email: {admin_data['email']}")
        print(f"   Password: {admin_data['password']}")
        print(f"   Role: ADMIN")
        print(f"   Organization: {admin_data['organization_name']}")
        print(f"   User ID: {user_id}")
        print(f"   Organization ID: {organization_id}")
        print("=" * 50)
        print("\n🔗 Login URL: http://localhost:9001/auth/login")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        client.close()


if __name__ == "__main__":
    asyncio.run(create_admin_user())
