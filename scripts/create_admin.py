#!/usr/bin/env python3
"""
Script para crear usuario administrador inicial en QAK

Este script crea un usuario con rol ADMIN y una organización administrativa.
Ejecutar solo una vez durante la configuración inicial.
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv(project_root / ".env")

from src.services.auth.password_service import PasswordService
from src.services.organization.organization_service import OrganizationService
from src.database.models.user import User
from src.database.models.organization import Organization
from src.database.models.user_organization import UserOrganization, Role
from src.database.connection import get_database
import motor.motor_asyncio


async def create_admin_user():
    """Create initial admin user and organization."""
    
    print("🚀 Creating QAK Admin User...")
    
    # Admin user data from environment variables
    admin_data = {
        "email": os.getenv("ADMIN_EMAIL", "<EMAIL>"),
        "password": os.getenv("ADMIN_PASSWORD", "AdminPassword123!"),
        "first_name": os.getenv("ADMIN_FIRST_NAME", "QAK"),
        "last_name": os.getenv("ADMIN_LAST_NAME", "Administrator"),
        "organization_name": os.getenv("ADMIN_ORGANIZATION", "QAK System Administration")
    }
    
    try:
        # Initialize services
        password_service = PasswordService()
        org_service = OrganizationService()
        
        # Connect to database using environment configuration
        mongodb_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
        mongodb_env = os.getenv("MONGODB_ENVIRONMENT", "development")

        # Configure client with SSL settings for Atlas
        client_options = {}
        if "mongodb+srv://" in mongodb_uri:
            client_options["tls"] = True
            client_options["tlsAllowInvalidCertificates"] = True

        client = motor.motor_asyncio.AsyncIOMotorClient(mongodb_uri, **client_options)
        db_name = f"qak_{mongodb_env}"
        db = client[db_name]

        print(f"🔗 Connected to database: {db_name}")

        # Test connection
        await client.admin.command('ping')
        print("✅ Database connection successful")
        
        print(f"📧 Creating admin user: {admin_data['email']}")
        
        # Check if admin user already exists
        existing_user = await db.users.find_one({"email": admin_data["email"]})
        if existing_user:
            print(f"⚠️  Admin user {admin_data['email']} already exists!")
            return
        
        # Create organization
        print(f"🏢 Creating organization: {admin_data['organization_name']}")
        organization = Organization(
            name=admin_data["organization_name"],
            slug="qak-system-admin",
            description="QAK System Administration Organization",
            settings={
                "is_system_org": True,
                "created_by_script": True
            }
        )
        
        # Save organization
        await db.organizations.insert_one(organization.model_dump())
        print(f"✅ Organization created with ID: {organization.organization_id}")
        
        # Hash password
        hashed_password = password_service.hash_password(admin_data["password"])
        
        # Create admin user
        admin_user = User(
            email=admin_data["email"],
            hashed_password=hashed_password,
            first_name=admin_data["first_name"],
            last_name=admin_data["last_name"],
            is_active=True
        )
        
        # Save user
        await db.users.insert_one(admin_user.model_dump())
        print(f"✅ Admin user created with ID: {admin_user.user_id}")
        
        # Create user-organization relationship with ADMIN role
        user_org = UserOrganization(
            user_id=admin_user.user_id,
            organization_id=organization.organization_id,
            role=Role.ADMIN,  # Super admin role
            joined_at=datetime.utcnow()
        )
        
        # Save relationship
        await db.user_organizations.insert_one(user_org.model_dump())
        print(f"✅ Admin role assigned to user")
        
        print("\n🎉 Admin user created successfully!")
        print("=" * 50)
        print("📋 Admin Credentials:")
        print(f"   Email: {admin_data['email']}")
        print(f"   Password: {admin_data['password']}")
        print(f"   Role: ADMIN")
        print(f"   Organization: {admin_data['organization_name']}")
        print("=" * 50)
        print("\n🔗 Login URL: http://localhost:3000/auth/login")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        raise
    finally:
        client.close()


if __name__ == "__main__":
    asyncio.run(create_admin_user())
