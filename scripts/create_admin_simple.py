#!/usr/bin/env python3
"""
Script súper simple para crear admin usando la API REST
"""

import requests
import json

def create_admin():
    print("🚀 Creando usuario admin...")
    
    # Datos del admin
    admin_data = {
        "email": "<EMAIL>",
        "password": "AdminPassword123!",
        "first_name": "Admin",
        "last_name": "User",
        "organization_name": "QAK Admin Organization"
    }
    
    try:
        # Verificar que el backend esté corriendo
        print("🔗 Verificando backend...")
        health = requests.get("http://localhost:8000/health", timeout=5)
        print(f"   Backend status: {health.status_code}")
        
        # Registrar admin
        print("📝 Registrando admin...")
        response = requests.post(
            "http://localhost:8000/auth/register",
            json=admin_data,
            timeout=10
        )
        
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Admin creado exitosamente!")
            data = response.json()
            print(f"   Email: {data['user']['email']}")
            print(f"   Organización: {data['organization']['name']}")
            print(f"   Rol: {data.get('user_organization', {}).get('role', 'N/A')}")
        elif response.status_code == 400:
            error = response.json()
            if "already exists" in error.get("detail", "").lower():
                print("⚠️  El usuario admin ya existe!")
                print("   Probando login...")
                
                # Probar login
                login_response = requests.post(
                    "http://localhost:8000/auth/login",
                    json={
                        "email": admin_data["email"],
                        "password": admin_data["password"]
                    },
                    timeout=10
                )
                
                if login_response.status_code == 200:
                    print("✅ Login exitoso!")
                    login_data = login_response.json()
                    print(f"   Email: {login_data['user']['email']}")
                    print(f"   Rol: {login_data.get('user_organization', {}).get('role', 'N/A')}")
                else:
                    print(f"❌ Login falló: {login_response.status_code}")
                    print(f"   Error: {login_response.text}")
            else:
                print(f"❌ Error en registro: {error.get('detail', 'Unknown')}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ No se puede conectar al backend")
        print("   Asegúrate de que esté corriendo:")
        print("   uvicorn src.main:app --reload --port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_admin()
