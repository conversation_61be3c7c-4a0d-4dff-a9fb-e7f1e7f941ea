#!/usr/bin/env python3
"""
Script para crear usuario administrador usando la API de QAK

Este script usa la API REST en lugar de conectarse directamente a la base de datos.
Útil cuando hay problemas de conectividad con MongoDB Atlas.
"""

import requests
import json
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
project_root = Path(__file__).parent.parent
load_dotenv(project_root / ".env")

def create_admin_via_api():
    """Create admin user via QAK API."""
    
    print("🚀 Creating QAK Admin User via API...")
    
    # API configuration
    api_url = "http://localhost:8000"
    
    # Admin user data from environment
    admin_data = {
        "email": os.getenv("ADMIN_EMAIL", "<EMAIL>"),
        "password": os.getenv("ADMIN_PASSWORD", "AdminPassword123!"),
        "first_name": os.getenv("ADMIN_FIRST_NAME", "QAK"),
        "last_name": os.getenv("ADMIN_LAST_NAME", "Administrator"),
        "organization_name": os.getenv("ADMIN_ORGANIZATION", "QAK System Administration")
    }
    
    print(f"📧 Creating admin user: {admin_data['email']}")
    print(f"🏢 Organization: {admin_data['organization_name']}")
    
    try:
        # Check if API is running
        print(f"🔗 Checking API health at {api_url}")
        health_response = requests.get(f"{api_url}/health", timeout=5)
        
        if health_response.status_code != 200:
            print(f"❌ API health check failed: {health_response.status_code}")
            print("💡 Make sure the backend is running: uvicorn src.main:app --reload --port 8000")
            return
        
        print("✅ API is running")
        
        # Register admin user
        print(f"📝 Registering admin user...")
        register_response = requests.post(
            f"{api_url}/auth/register",
            json={
                "email": admin_data["email"],
                "password": admin_data["password"],
                "first_name": admin_data["first_name"],
                "last_name": admin_data["last_name"],
                "organization_name": admin_data["organization_name"]
            },
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if register_response.status_code == 201:
            response_data = register_response.json()
            print("✅ Admin user created successfully!")
            print("=" * 50)
            print("📋 Admin Credentials:")
            print(f"   Email: {admin_data['email']}")
            print(f"   Password: {admin_data['password']}")
            print(f"   Role: {response_data.get('user_organization', {}).get('role', 'ORG_ADMIN')}")
            print(f"   Organization: {admin_data['organization_name']}")
            print("=" * 50)
            print("\n🔗 Login URL: http://localhost:3000/auth/login")
            
        elif register_response.status_code == 400:
            error_data = register_response.json()
            if "already exists" in error_data.get("detail", "").lower():
                print("⚠️  Admin user already exists!")
                print("=" * 50)
                print("📋 Existing Admin Credentials:")
                print(f"   Email: {admin_data['email']}")
                print(f"   Password: {admin_data['password']}")
                print("=" * 50)
                print("\n🔗 Login URL: http://localhost:3000/auth/login")
            else:
                print(f"❌ Registration failed: {error_data.get('detail', 'Unknown error')}")
        else:
            print(f"❌ Registration failed with status {register_response.status_code}")
            try:
                error_data = register_response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Response text: {register_response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API")
        print("💡 Make sure the backend is running:")
        print("   uvicorn src.main:app --reload --port 8000")
    except requests.exceptions.Timeout:
        print("❌ API request timed out")
        print("💡 The backend might be slow to respond")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    create_admin_via_api()
