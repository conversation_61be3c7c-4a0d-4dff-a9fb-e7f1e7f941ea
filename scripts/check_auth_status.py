#!/usr/bin/env python3
"""
Script para verificar el estado del sistema de autenticación QAK

Este script verifica:
- Conexión a la base de datos
- Usuarios existentes
- Organizaciones creadas
- Estado de autenticación
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import motor.motor_asyncio
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv(project_root / ".env")


async def check_auth_status():
    """Check authentication system status."""
    
    print("🔍 QAK Authentication System Status Check")
    print("=" * 50)
    
    try:
        # Connect to database
        mongodb_uri = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
        mongodb_env = os.getenv("MONGODB_ENVIRONMENT", "development")
        
        client = motor.motor_asyncio.AsyncIOMotorClient(mongodb_uri)
        db_name = f"qak_{mongodb_env}"
        db = client[db_name]
        
        print(f"🔗 Database: {db_name}")
        print(f"📡 MongoDB URI: {mongodb_uri}")
        
        # Check database connection
        await client.admin.command('ping')
        print("✅ Database connection: OK")
        
        # Check collections
        collections = await db.list_collection_names()
        auth_collections = ['users', 'organizations', 'user_organizations']
        
        print(f"\n📊 Collections found: {len(collections)}")
        for collection in auth_collections:
            if collection in collections:
                count = await db[collection].count_documents({})
                print(f"   ✅ {collection}: {count} documents")
            else:
                print(f"   ❌ {collection}: Not found")
        
        # Check users
        print(f"\n👥 Users Analysis:")
        users = await db.users.find({}).to_list(length=None)
        
        if not users:
            print("   ❌ No users found")
            print("   💡 Run: python scripts/create_admin.py")
        else:
            print(f"   ✅ Total users: {len(users)}")
            
            for user in users:
                # Get user organization info
                user_org = await db.user_organizations.find_one({"user_id": user["user_id"]})
                org = await db.organizations.find_one({"organization_id": user_org["organization_id"]}) if user_org else None
                
                role = user_org["role"] if user_org else "NO_ROLE"
                org_name = org["name"] if org else "NO_ORG"
                
                print(f"   📧 {user['email']} | {role} | {org_name}")
        
        # Check organizations
        print(f"\n🏢 Organizations Analysis:")
        orgs = await db.organizations.find({}).to_list(length=None)
        
        if not orgs:
            print("   ❌ No organizations found")
        else:
            print(f"   ✅ Total organizations: {len(orgs)}")
            
            for org in orgs:
                # Count members
                member_count = await db.user_organizations.count_documents({"organization_id": org["organization_id"]})
                print(f"   🏢 {org['name']} | {member_count} members | {org['slug']}")
        
        # Check environment configuration
        print(f"\n⚙️  Environment Configuration:")
        jwt_secret = os.getenv("JWT_SECRET_KEY", "NOT_SET")
        jwt_secret_display = jwt_secret[:10] + "..." if len(jwt_secret) > 10 else jwt_secret
        
        print(f"   🔐 JWT_SECRET_KEY: {jwt_secret_display}")
        print(f"   ⏰ ACCESS_TOKEN_EXPIRE: {os.getenv('JWT_ACCESS_TOKEN_EXPIRE_MINUTES', 'NOT_SET')} minutes")
        print(f"   🔄 REFRESH_TOKEN_EXPIRE: {os.getenv('JWT_REFRESH_TOKEN_EXPIRE_DAYS', 'NOT_SET')} days")
        print(f"   🔒 PASSWORD_BCRYPT_ROUNDS: {os.getenv('PASSWORD_BCRYPT_ROUNDS', 'NOT_SET')}")
        
        # Check admin credentials
        print(f"\n👑 Admin Configuration:")
        admin_email = os.getenv("ADMIN_EMAIL", "NOT_SET")
        admin_password = os.getenv("ADMIN_PASSWORD", "NOT_SET")
        admin_password_display = admin_password[:5] + "..." if len(admin_password) > 5 else admin_password
        
        print(f"   📧 ADMIN_EMAIL: {admin_email}")
        print(f"   🔑 ADMIN_PASSWORD: {admin_password_display}")
        
        # Check if admin user exists
        if admin_email != "NOT_SET":
            admin_user = await db.users.find_one({"email": admin_email})
            if admin_user:
                print(f"   ✅ Admin user exists in database")
                
                # Check admin role
                admin_user_org = await db.user_organizations.find_one({"user_id": admin_user["user_id"]})
                if admin_user_org and admin_user_org["role"] == "ADMIN":
                    print(f"   ✅ Admin has ADMIN role")
                else:
                    print(f"   ⚠️  Admin role: {admin_user_org['role'] if admin_user_org else 'NO_ROLE'}")
            else:
                print(f"   ❌ Admin user not found in database")
                print(f"   💡 Run: python scripts/create_admin.py")
        
        # API Status
        print(f"\n🚀 API Status:")
        print(f"   🔗 Expected API URL: http://localhost:8000")
        print(f"   📚 API Docs: http://localhost:8000/docs")
        print(f"   🌐 Frontend URL: http://localhost:3000")
        
        # Quick test recommendations
        print(f"\n🧪 Quick Test Recommendations:")
        print(f"   1. Start backend: uvicorn src.main:app --reload --port 8000")
        print(f"   2. Start frontend: cd web && npm run dev")
        print(f"   3. Navigate to: http://localhost:3000/auth/login")
        
        if admin_email != "NOT_SET" and admin_password != "NOT_SET":
            print(f"   4. Login with: {admin_email} / {admin_password}")
        else:
            print(f"   4. Register new user or run create_admin.py script")
        
        print(f"\n✅ Authentication system check completed!")
        
    except Exception as e:
        print(f"❌ Error during status check: {e}")
        print(f"\n🔧 Troubleshooting:")
        print(f"   - Verify MongoDB is running")
        print(f"   - Check MONGODB_URI in .env file")
        print(f"   - Ensure database permissions are correct")
        raise
    finally:
        client.close()


if __name__ == "__main__":
    asyncio.run(check_auth_status())
